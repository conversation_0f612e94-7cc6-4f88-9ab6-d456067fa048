# Backend Architecture

```mermaid
graph TD
    subgraph "API Layer (FastAPI)"
        FastAPI[FastAPI Application] --> APIRouter[API Router]
        FastAPI --> Middleware[Middleware]
        FastAPI --> ErrorHandlers[Error Handlers]
        
        Middleware --> CORSMiddleware[CORS Middleware]
        Middleware --> AuthMiddleware[Authentication Middleware]
        Middleware --> LoggingMiddleware[Logging Middleware]
        
        APIRouter --> ProjectsRouter[Projects Router]
        APIRouter --> TasksRouter[Tasks Router]
        APIRouter --> LLMRouter[LLM Router]
        APIRouter --> VisualizationRouter[Visualization Router]
        APIRouter --> SettingsRouter[Settings Router]
        
        %% Projects Endpoints
        ProjectsRouter --> GetProjectsEndpoint[GET /projects]
        ProjectsRouter --> GetProjectEndpoint[GET /projects/{id}]
        ProjectsRouter --> CreateProjectEndpoint[POST /projects]
        ProjectsRouter --> UpdateProjectEndpoint[PATCH /projects/{id}]
        ProjectsRouter --> DeleteProjectEndpoint[DELETE /projects/{id}]
        ProjectsRouter --> ScanDirectoryEndpoint[POST /projects/scan]
        
        %% Tasks Endpoints
        TasksRouter --> GetTasksEndpoint[GET /projects/{id}/tasks]
        TasksRouter --> GetTaskEndpoint[GET /projects/{id}/tasks/{task_id}]
        TasksRouter --> CreateTaskEndpoint[POST /projects/{id}/tasks]
        TasksRouter --> UpdateTaskEndpoint[PATCH /projects/{id}/tasks/{task_id}]
        TasksRouter --> DeleteTaskEndpoint[DELETE /projects/{id}/tasks/{task_id}]
        
        %% LLM Endpoints
        LLMRouter --> GenerateTextEndpoint[POST /llm/generate]
        LLMRouter --> GetModelsEndpoint[GET /llm/models]
        LLMRouter --> GetProvidersEndpoint[GET /llm/providers]
        
        %% Visualization Endpoints
        VisualizationRouter --> GenerateGraphEndpoint[POST /visualization/graph]
        VisualizationRouter --> ExportGraphEndpoint[POST /visualization/export]
        
        %% Settings Endpoints
        SettingsRouter --> GetSettingsEndpoint[GET /settings]
        SettingsRouter --> UpdateSettingsEndpoint[PATCH /settings]
    end
    
    subgraph "Backend Services"
        %% Project Services
        ProjectService[Project Service] --> ProjectRepository[Project Repository]
        ProjectService --> DirectoryScanner[Directory Scanner]
        ProjectService --> ProjectValidator[Project Validator]
        
        %% Task Services
        TaskService[Task Service] --> TaskRepository[Task Repository]
        TaskService --> TaskValidator[Task Validator]
        TaskService --> TaskAnalyzer[Task Analyzer]
        
        %% LLM Services
        LLMService[LLM Service] --> OpenAIProvider[OpenAI Provider]
        LLMService --> AnthropicProvider[Anthropic Provider]
        LLMService --> LocalLLMProvider[Local LLM Provider]
        LLMService --> PromptTemplates[Prompt Templates]
        
        %% Visualization Services
        VisualizationService[Visualization Service] --> GraphGenerator[Graph Generator]
        VisualizationService --> ImageExporter[Image Exporter]
        VisualizationService --> LayoutEngine[Layout Engine]
        
        %% Settings Services
        SettingsService[Settings Service] --> ConfigRepository[Config Repository]
        SettingsService --> EnvironmentManager[Environment Manager]
        
        %% Shared Services
        LoggingService[Logging Service]
        AuthService[Auth Service]
        FileSystemService[File System Service]
    end
    
    subgraph "Data Models"
        %% Project Models
        ProjectModel[Project Model]
        ProjectCreateModel[Project Create Model]
        ProjectUpdateModel[Project Update Model]
        
        %% Task Models
        TaskModel[Task Model]
        TaskCreateModel[Task Create Model]
        TaskUpdateModel[Task Update Model]
        
        %% LLM Models
        LLMRequestModel[LLM Request Model]
        LLMResponseModel[LLM Response Model]
        ModelInfoModel[Model Info Model]
        
        %% Visualization Models
        GraphRequestModel[Graph Request Model]
        GraphResponseModel[Graph Response Model]
        ExportRequestModel[Export Request Model]
        
        %% Settings Models
        SettingsModel[Settings Model]
        APIKeyModel[API Key Model]
        UserPreferencesModel[User Preferences Model]
    end
    
    subgraph "Storage Layer"
        %% File System Storage
        FileSystemStorage[File System Storage] --> VibeArchFolder[.VibeArch Folder]
        
        VibeArchFolder --> DirectoryFolder[Directory Folder]
        VibeArchFolder --> SpecificationFolder[Specification Folder]
        VibeArchFolder --> ArchitectureFolder[Architecture Folder]
        VibeArchFolder --> TechStackFolder[TechStack Folder]
        VibeArchFolder --> DocumentationFolder[Documentation Folder]
        
        %% File Operations
        FileOperations[File Operations] --> ReadOperation[Read Operation]
        FileOperations --> WriteOperation[Write Operation]
        FileOperations --> DeleteOperation[Delete Operation]
        FileOperations --> ListOperation[List Operation]
        
        %% Data Serialization
        DataSerialization[Data Serialization] --> JSONSerialization[JSON Serialization]
        DataSerialization --> YAMLSerialization[YAML Serialization]
        DataSerialization --> MarkdownSerialization[Markdown Serialization]
    end
    
    %% Connections between layers
    GetProjectsEndpoint --> ProjectService
    GetProjectEndpoint --> ProjectService
    CreateProjectEndpoint --> ProjectService
    UpdateProjectEndpoint --> ProjectService
    DeleteProjectEndpoint --> ProjectService
    ScanDirectoryEndpoint --> ProjectService
    
    GetTasksEndpoint --> TaskService
    GetTaskEndpoint --> TaskService
    CreateTaskEndpoint --> TaskService
    UpdateTaskEndpoint --> TaskService
    DeleteTaskEndpoint --> TaskService
    
    GenerateTextEndpoint --> LLMService
    GetModelsEndpoint --> LLMService
    GetProvidersEndpoint --> LLMService
    
    GenerateGraphEndpoint --> VisualizationService
    ExportGraphEndpoint --> VisualizationService
    
    GetSettingsEndpoint --> SettingsService
    UpdateSettingsEndpoint --> SettingsService
    
    ProjectService --> FileSystemService
    TaskService --> FileSystemService
    SettingsService --> FileSystemService
    
    FileSystemService --> FileSystemStorage
    FileSystemService --> FileOperations
    FileSystemService --> DataSerialization
    
    classDef api fill:#42a5f5,stroke:#1976d2,color:white;
    classDef service fill:#7e57c2,stroke:#4527a0,color:white;
    classDef model fill:#26a69a,stroke:#00796b,color:white;
    classDef storage fill:#ef5350,stroke:#c62828,color:white;
    
    class FastAPI,APIRouter,Middleware,ErrorHandlers,CORSMiddleware,AuthMiddleware,LoggingMiddleware,ProjectsRouter,TasksRouter,LLMRouter,VisualizationRouter,SettingsRouter,GetProjectsEndpoint,GetProjectEndpoint,CreateProjectEndpoint,UpdateProjectEndpoint,DeleteProjectEndpoint,ScanDirectoryEndpoint,GetTasksEndpoint,GetTaskEndpoint,CreateTaskEndpoint,UpdateTaskEndpoint,DeleteTaskEndpoint,GenerateTextEndpoint,GetModelsEndpoint,GetProvidersEndpoint,GenerateGraphEndpoint,ExportGraphEndpoint,GetSettingsEndpoint,UpdateSettingsEndpoint api;
    class ProjectService,ProjectRepository,DirectoryScanner,ProjectValidator,TaskService,TaskRepository,TaskValidator,TaskAnalyzer,LLMService,OpenAIProvider,AnthropicProvider,LocalLLMProvider,PromptTemplates,VisualizationService,GraphGenerator,ImageExporter,LayoutEngine,SettingsService,ConfigRepository,EnvironmentManager,LoggingService,AuthService,FileSystemService service;
    class ProjectModel,ProjectCreateModel,ProjectUpdateModel,TaskModel,TaskCreateModel,TaskUpdateModel,LLMRequestModel,LLMResponseModel,ModelInfoModel,GraphRequestModel,GraphResponseModel,ExportRequestModel,SettingsModel,APIKeyModel,UserPreferencesModel model;
    class FileSystemStorage,VibeArchFolder,DirectoryFolder,SpecificationFolder,ArchitectureFolder,TechStackFolder,DocumentationFolder,FileOperations,ReadOperation,WriteOperation,DeleteOperation,ListOperation,DataSerialization,JSONSerialization,YAMLSerialization,MarkdownSerialization storage;
```
