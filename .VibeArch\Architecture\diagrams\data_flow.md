# Data Flow Architecture

```mermaid
flowchart TD
    User([User]) -->|Interacts with| UI[User Interface]
    
    subgraph "Frontend"
        UI -->|Displays| ProjectsView[Projects View]
        UI -->|Displays| TasksView[Tasks View]
        UI -->|Displays| LLMView[LLM View]
        UI -->|Displays| VisualizationView[Visualization View]
        UI -->|Displays| SettingsView[Settings View]
        
        ProjectsView -->|User Action| ProjectsController[Projects Controller]
        TasksView -->|User Action| TasksController[Tasks Controller]
        LLMView -->|User Action| LLMController[LLM Controller]
        VisualizationView -->|User Action| VisualizationController[Visualization Controller]
        SettingsView -->|User Action| SettingsController[Settings Controller]
        
        ProjectsController -->|Updates| ProjectsView
        TasksController -->|Updates| TasksView
        LLMController -->|Updates| LLMView
        VisualizationController -->|Updates| VisualizationView
        SettingsController -->|Updates| SettingsView
        
        ProjectsController -->|API Request| APIClient[API Client]
        TasksController -->|API Request| APIClient
        LLMController -->|API Request| APIClient
        VisualizationController -->|API Request| APIClient
        SettingsController -->|API Request| APIClient
        
        APIClient -->|Response Data| ProjectsController
        APIClient -->|Response Data| TasksController
        APIClient -->|Response Data| LLMController
        APIClient -->|Response Data| VisualizationController
        APIClient -->|Response Data| SettingsController
    end
    
    APIClient -->|HTTP Request| APIGateway[API Gateway]
    APIGateway -->|HTTP Response| APIClient
    
    subgraph "Backend"
        APIGateway -->|Routes Request| ProjectsAPI[Projects API]
        APIGateway -->|Routes Request| TasksAPI[Tasks API]
        APIGateway -->|Routes Request| LLMAPI[LLM API]
        APIGateway -->|Routes Request| VisualizationAPI[Visualization API]
        APIGateway -->|Routes Request| SettingsAPI[Settings API]
        
        ProjectsAPI -->|Response| APIGateway
        TasksAPI -->|Response| APIGateway
        LLMAPI -->|Response| APIGateway
        VisualizationAPI -->|Response| APIGateway
        SettingsAPI -->|Response| APIGateway
        
        ProjectsAPI -->|Processes Request| ProjectsService[Projects Service]
        TasksAPI -->|Processes Request| TasksService[Tasks Service]
        LLMAPI -->|Processes Request| LLMService[LLM Service]
        VisualizationAPI -->|Processes Request| VisualizationService[Visualization Service]
        SettingsAPI -->|Processes Request| SettingsService[Settings Service]
        
        ProjectsService -->|Returns Data| ProjectsAPI
        TasksService -->|Returns Data| TasksAPI
        LLMService -->|Returns Data| LLMAPI
        VisualizationService -->|Returns Data| VisualizationAPI
        SettingsService -->|Returns Data| SettingsAPI
        
        ProjectsService -->|Reads/Writes| FileSystemService[File System Service]
        TasksService -->|Reads/Writes| FileSystemService
        VisualizationService -->|Reads| FileSystemService
        SettingsService -->|Reads/Writes| FileSystemService
        
        LLMService -->|API Request| ExternalLLMProviders[External LLM Providers]
        ExternalLLMProviders -->|API Response| LLMService
    end
    
    FileSystemService -->|File Operations| Storage[File System Storage]
    
    subgraph "Storage"
        Storage -->|Stores| VibeArchFolder[.VibeArch Folder]
        
        VibeArchFolder -->|Contains| DirectoryData[Directory Data]
        VibeArchFolder -->|Contains| SpecificationData[Specification Data]
        VibeArchFolder -->|Contains| ArchitectureData[Architecture Data]
        VibeArchFolder -->|Contains| TechStackData[TechStack Data]
        VibeArchFolder -->|Contains| DocumentationData[Documentation Data]
        
        DirectoryData -->|Stored as| DirectoryFiles[JSON Files]
        SpecificationData -->|Stored as| SpecificationFiles[Markdown Files]
        ArchitectureData -->|Stored as| ArchitectureFiles[JSON & Markdown Files]
        TechStackData -->|Stored as| TechStackFiles[JSON Files]
        DocumentationData -->|Stored as| DocumentationFiles[Markdown Files]
    end
    
    %% Data Flow for Project Creation
    User -->|1. Creates Project| UI
    UI -->|2. Submits Form| ProjectsController
    ProjectsController -->|3. Sends Request| APIClient
    APIClient -->|4. HTTP POST| APIGateway
    APIGateway -->|5. Routes to| ProjectsAPI
    ProjectsAPI -->|6. Processes| ProjectsService
    ProjectsService -->|7. Creates Files| FileSystemService
    FileSystemService -->|8. Writes to| Storage
    Storage -->|9. Stores in| VibeArchFolder
    FileSystemService -->|10. Returns Success| ProjectsService
    ProjectsService -->|11. Returns Data| ProjectsAPI
    ProjectsAPI -->|12. Returns Response| APIGateway
    APIGateway -->|13. HTTP Response| APIClient
    APIClient -->|14. Updates State| ProjectsController
    ProjectsController -->|15. Updates UI| ProjectsView
    ProjectsView -->|16. Shows Success| User
    
    %% Data Flow for LLM Interaction
    User -->|1. Enters Prompt| UI
    UI -->|2. Submits Prompt| LLMController
    LLMController -->|3. Sends Request| APIClient
    APIClient -->|4. HTTP POST| APIGateway
    APIGateway -->|5. Routes to| LLMAPI
    LLMAPI -->|6. Processes| LLMService
    LLMService -->|7. Sends to| ExternalLLMProviders
    ExternalLLMProviders -->|8. Returns Response| LLMService
    LLMService -->|9. Processes Response| LLMAPI
    LLMAPI -->|10. Returns Response| APIGateway
    APIGateway -->|11. HTTP Response| APIClient
    APIClient -->|12. Updates State| LLMController
    LLMController -->|13. Updates UI| LLMView
    LLMView -->|14. Shows Response| User
    
    classDef frontend fill:#42a5f5,stroke:#1976d2,color:white;
    classDef api fill:#7e57c2,stroke:#4527a0,color:white;
    classDef service fill:#26a69a,stroke:#00796b,color:white;
    classDef storage fill:#ef5350,stroke:#c62828,color:white;
    classDef external fill:#ffa726,stroke:#ef6c00,color:white;
    classDef user fill:#78909c,stroke:#455a64,color:white;
    
    class UI,ProjectsView,TasksView,LLMView,VisualizationView,SettingsView,ProjectsController,TasksController,LLMController,VisualizationController,SettingsController,APIClient frontend;
    class APIGateway,ProjectsAPI,TasksAPI,LLMAPI,VisualizationAPI,SettingsAPI api;
    class ProjectsService,TasksService,LLMService,VisualizationService,SettingsService,FileSystemService service;
    class Storage,VibeArchFolder,DirectoryData,SpecificationData,ArchitectureData,TechStackData,DocumentationData,DirectoryFiles,SpecificationFiles,ArchitectureFiles,TechStackFiles,DocumentationFiles storage;
    class ExternalLLMProviders external;
    class User user;
```
