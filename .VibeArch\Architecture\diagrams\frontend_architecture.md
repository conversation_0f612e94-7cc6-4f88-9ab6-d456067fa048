# Frontend Architecture

```mermaid
graph TD
    subgraph "Frontend Application"
        App[App.jsx] --> Router[React Router]
        Router --> Routes[Routes Configuration]
        
        Routes --> LayoutComponent[Layout Component]
        
        LayoutComponent --> NavbarComponent[Navbar Component]
        LayoutComponent --> SidebarComponent[Sidebar Component]
        LayoutComponent --> ContentArea[Content Area]
        
        ContentArea --> DashboardPage[Dashboard Page]
        ContentArea --> ProjectsPage[Projects Page]
        ContentArea --> TasksPage[Tasks Page]
        ContentArea --> LLMPage[LLM Interface Page]
        ContentArea --> VisualizationPage[Visualization Page]
        ContentArea --> SettingsPage[Settings Page]
        
        %% Dashboard Components
        DashboardPage --> ProjectStatsComponent[Project Stats Component]
        DashboardPage --> RecentProjectsComponent[Recent Projects Component]
        DashboardPage --> QuickActionsComponent[Quick Actions Component]
        
        ProjectStatsComponent --> StatsCardComponent[Stats Card Component]
        RecentProjectsComponent --> ProjectCardComponent[Project Card Component]
        
        %% Projects Components
        ProjectsPage --> ProjectListComponent[Project List Component]
        ProjectsPage --> ProjectFormComponent[Project Form Component]
        ProjectsPage --> DirectorySelectorComponent[Directory Selector Component]
        
        ProjectListComponent --> ProjectItemComponent[Project Item Component]
        ProjectFormComponent --> FormInputComponent[Form Input Component]
        DirectorySelectorComponent --> FileExplorerComponent[File Explorer Component]
        
        %% Tasks Components
        TasksPage --> TaskListComponent[Task List Component]
        TasksPage --> TaskFormComponent[Task Form Component]
        TasksPage --> TaskFilterComponent[Task Filter Component]
        
        TaskListComponent --> TaskItemComponent[Task Item Component]
        TaskFormComponent --> FormInputComponent
        
        %% LLM Components
        LLMPage --> PromptFormComponent[Prompt Form Component]
        LLMPage --> ModelSelectorComponent[Model Selector Component]
        LLMPage --> ResponseViewerComponent[Response Viewer Component]
        
        PromptFormComponent --> FormInputComponent
        ModelSelectorComponent --> DropdownComponent[Dropdown Component]
        
        %% Visualization Components
        VisualizationPage --> GraphViewComponent[Graph View Component]
        VisualizationPage --> GraphControlsComponent[Graph Controls Component]
        VisualizationPage --> ExportComponent[Export Component]
        
        GraphViewComponent --> D3Integration[D3.js Integration]
        
        %% Settings Components
        SettingsPage --> APISettingsComponent[API Settings Component]
        SettingsPage --> UserSettingsComponent[User Settings Component]
        SettingsPage --> AppSettingsComponent[App Settings Component]
        
        APISettingsComponent --> FormInputComponent
        UserSettingsComponent --> FormInputComponent
        AppSettingsComponent --> FormInputComponent
        
        %% Shared Components
        SharedComponents[Shared Components] --> ButtonComponent[Button Component]
        SharedComponents --> CardComponent[Card Component]
        SharedComponents --> ModalComponent[Modal Component]
        SharedComponents --> AlertComponent[Alert Component]
        SharedComponents --> LoadingComponent[Loading Component]
        SharedComponents --> FormInputComponent
        SharedComponents --> DropdownComponent
        
        %% Services
        Services[Services] --> APIService[API Service]
        Services --> AuthService[Auth Service]
        Services --> StorageService[Storage Service]
        Services --> NotificationService[Notification Service]
        
        APIService --> ProjectsAPI[Projects API]
        APIService --> TasksAPI[Tasks API]
        APIService --> LLMAPI[LLM API]
        APIService --> VisualizationAPI[Visualization API]
        APIService --> SettingsAPI[Settings API]
        
        %% Hooks
        Hooks[Custom Hooks] --> UseProjectsHook[useProjects Hook]
        Hooks --> UseTasksHook[useTasks Hook]
        Hooks --> UseLLMHook[useLLM Hook]
        Hooks --> UseVisualizationHook[useVisualization Hook]
        Hooks --> UseSettingsHook[useSettings Hook]
        Hooks --> UseAPIHook[useAPI Hook]
        
        %% State Management
        StateManagement[State Management] --> ReactHooks[React Hooks]
        ReactHooks --> UseStateHook[useState]
        ReactHooks --> UseEffectHook[useEffect]
        ReactHooks --> UseContextHook[useContext]
        ReactHooks --> UseReducerHook[useReducer]
        
        %% Styling
        Styling[Styling] --> TailwindCSS[Tailwind CSS]
        TailwindCSS --> UtilityClasses[Utility Classes]
        TailwindCSS --> ResponsiveDesign[Responsive Design]
        TailwindCSS --> DarkMode[Dark Mode]
        
        %% Build Tools
        BuildTools[Build Tools] --> Vite[Vite]
        Vite --> HMR[Hot Module Replacement]
        Vite --> ESBuild[ESBuild]
        Vite --> Plugins[Vite Plugins]
    end
    
    classDef component fill:#42a5f5,stroke:#1976d2,color:white;
    classDef page fill:#7e57c2,stroke:#4527a0,color:white;
    classDef service fill:#26a69a,stroke:#00796b,color:white;
    classDef hook fill:#ef5350,stroke:#c62828,color:white;
    classDef tool fill:#ffa726,stroke:#ef6c00,color:white;
    
    class App,Router,Routes,LayoutComponent,NavbarComponent,SidebarComponent,ContentArea component;
    class DashboardPage,ProjectsPage,TasksPage,LLMPage,VisualizationPage,SettingsPage page;
    class APIService,AuthService,StorageService,NotificationService,ProjectsAPI,TasksAPI,LLMAPI,VisualizationAPI,SettingsAPI service;
    class UseProjectsHook,UseTasksHook,UseLLMHook,UseVisualizationHook,UseSettingsHook,UseAPIHook,UseStateHook,UseEffectHook,UseContextHook,UseReducerHook hook;
    class Vite,HMR,ESBuild,Plugins,TailwindCSS,UtilityClasses,ResponsiveDesign,DarkMode tool;
```
