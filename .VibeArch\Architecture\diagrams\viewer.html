<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vibe Architect - Architecture Diagrams</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.2.3/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        h1 {
            color: #2563eb;
        }
        h2 {
            color: #4b5563;
            margin-top: 30px;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
            background-color: #f9fafb;
        }
        .tab.active {
            background-color: #fff;
            border-color: #e5e7eb;
            border-bottom-color: #fff;
            font-weight: bold;
            color: #2563eb;
        }
        .diagram-container {
            display: none;
            background-color: #fff;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 0 4px 4px 4px;
            margin-top: -1px;
        }
        .diagram-container.active {
            display: block;
        }
        .mermaid {
            overflow: auto;
        }
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 18px;
            color: #6b7280;
        }
        .zoom-controls {
            margin-bottom: 10px;
        }
        .zoom-controls button {
            background-color: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 5px 10px;
            margin-right: 5px;
            cursor: pointer;
        }
        .zoom-controls button:hover {
            background-color: #e5e7eb;
        }
        .description {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9fafb;
            border-radius: 4px;
            border-left: 4px solid #2563eb;
        }
    </style>
</head>
<body>
    <header>
        <h1>Vibe Architect - Architecture Diagrams</h1>
        <p>Interactive visualizations of the Vibe Architect architecture</p>
    </header>

    <div class="tabs">
        <div class="tab active" data-target="system">System Architecture</div>
        <div class="tab" data-target="frontend">Frontend Architecture</div>
        <div class="tab" data-target="backend">Backend Architecture</div>
        <div class="tab" data-target="dataflow">Data Flow</div>
    </div>

    <div id="system" class="diagram-container active">
        <div class="description">
            <h2>System Architecture</h2>
            <p>This diagram provides a high-level overview of the entire Vibe Architect system, showing the relationships between frontend, API, backend, and storage components.</p>
        </div>
        <div class="zoom-controls">
            <button onclick="zoomIn('system-diagram')">Zoom In</button>
            <button onclick="zoomOut('system-diagram')">Zoom Out</button>
            <button onclick="resetZoom('system-diagram')">Reset</button>
        </div>
        <div id="system-diagram" class="mermaid">
            graph TD
                User[User] -->|interacts with| Frontend
                
                subgraph "Frontend (Vite + React)"
                    Frontend[Frontend Application] --> NavBar[Navigation Bar]
                    Frontend --> Sidebar[Sidebar]
                    Frontend --> MainContent[Main Content Area]
                    
                    MainContent --> Dashboard[Dashboard]
                    MainContent --> Projects[Projects Management]
                    MainContent --> Tasks[Tasks Management]
                    MainContent --> LLM[LLM Interface]
                    MainContent --> Visualization[Visualization]
                    MainContent --> Settings[Settings]
                    
                    Dashboard --> ProjectStats[Project Statistics]
                    Dashboard --> RecentProjects[Recent Projects]
                    
                    Projects --> ProjectList[Project List]
                    Projects --> ProjectDetails[Project Details]
                    Projects --> ProjectCreation[Project Creation]
                    
                    Tasks --> TaskList[Task List]
                    Tasks --> TaskDetails[Task Details]
                    Tasks --> TaskCreation[Task Creation]
                    
                    LLM --> PromptInput[Prompt Input]
                    LLM --> ModelSelection[Model Selection]
                    LLM --> ResponseDisplay[Response Display]
                    
                    Visualization --> GraphView[Graph View]
                    Visualization --> ExportOptions[Export Options]
                    
                    Settings --> APIKeys[API Keys]
                    Settings --> Preferences[User Preferences]
                end
                
                Frontend -->|HTTP Requests| API
                
                subgraph "API Layer (FastAPI)"
                    API[API Gateway] --> ProjectsAPI[Projects API]
                    API --> TasksAPI[Tasks API]
                    API --> LLMAPI[LLM API]
                    API --> VisualizationAPI[Visualization API]
                    API --> SettingsAPI[Settings API]
                    
                    ProjectsAPI --> ProjectsCRUD[Projects CRUD]
                    ProjectsAPI --> ProjectScanning[Project Scanning]
                    
                    TasksAPI --> TasksCRUD[Tasks CRUD]
                    TasksAPI --> TaskDependencies[Task Dependencies]
                    
                    LLMAPI --> TextGeneration[Text Generation]
                    LLMAPI --> ModelManagement[Model Management]
                    
                    VisualizationAPI --> GraphGeneration[Graph Generation]
                    VisualizationAPI --> GraphExport[Graph Export]
                    
                    SettingsAPI --> UserSettings[User Settings]
                    SettingsAPI --> AppSettings[App Settings]
                end
                
                API -->|Internal Calls| Backend
                
                subgraph "Backend (Python)"
                    Backend[Backend Services] --> ProjectManager[Project Manager]
                    Backend --> TaskManager[Task Manager]
                    Backend --> LLMService[LLM Service]
                    Backend --> VisualizationService[Visualization Service]
                    Backend --> ConfigManager[Configuration Manager]
                    
                    ProjectManager --> FileSystem[File System Access]
                    ProjectManager --> ProjectIndexing[Project Indexing]
                    
                    TaskManager --> TaskStorage[Task Storage]
                    TaskManager --> TaskAnalysis[Task Analysis]
                    
                    LLMService --> ProviderIntegration[Provider Integration]
                    LLMService --> PromptManagement[Prompt Management]
                    
                    VisualizationService --> GraphAlgorithms[Graph Algorithms]
                    VisualizationService --> ImageGeneration[Image Generation]
                    
                    ConfigManager --> SettingsStorage[Settings Storage]
                    ConfigManager --> EnvironmentVariables[Environment Variables]
                end
                
                Backend -->|File Operations| Storage
                
                subgraph "Storage"
                    Storage[File-based Storage] --> VibeArchFolder[.VibeArch Folder]
                    
                    VibeArchFolder --> DirectoryFolder[Directory]
                    VibeArchFolder --> SpecificationFolder[Specification]
                    VibeArchFolder --> ArchitectureFolder[Architecture]
                    VibeArchFolder --> TechStackFolder[TechStack]
                    VibeArchFolder --> DocumentationFolder[Documentation]
                end
                
                classDef frontend fill:#42a5f5,stroke:#1976d2,color:white;
                classDef api fill:#7e57c2,stroke:#4527a0,color:white;
                classDef backend fill:#26a69a,stroke:#00796b,color:white;
                classDef storage fill:#ef5350,stroke:#c62828,color:white;
                
                class Frontend,NavBar,Sidebar,MainContent,Dashboard,Projects,Tasks,LLM,Visualization,Settings,ProjectStats,RecentProjects,ProjectList,ProjectDetails,ProjectCreation,TaskList,TaskDetails,TaskCreation,PromptInput,ModelSelection,ResponseDisplay,GraphView,ExportOptions,APIKeys,Preferences frontend;
                class API,ProjectsAPI,TasksAPI,LLMAPI,VisualizationAPI,SettingsAPI,ProjectsCRUD,ProjectScanning,TasksCRUD,TaskDependencies,TextGeneration,ModelManagement,GraphGeneration,GraphExport,UserSettings,AppSettings api;
                class Backend,ProjectManager,TaskManager,LLMService,VisualizationService,ConfigManager,FileSystem,ProjectIndexing,TaskStorage,TaskAnalysis,ProviderIntegration,PromptManagement,GraphAlgorithms,ImageGeneration,SettingsStorage,EnvironmentVariables backend;
                class Storage,VibeArchFolder,DirectoryFolder,SpecificationFolder,ArchitectureFolder,TechStackFolder,DocumentationFolder storage;
        </div>
    </div>

    <div id="frontend" class="diagram-container">
        <div class="description">
            <h2>Frontend Architecture</h2>
            <p>This diagram shows the detailed structure of the frontend components, including pages, components, services, and hooks.</p>
        </div>
        <div class="zoom-controls">
            <button onclick="zoomIn('frontend-diagram')">Zoom In</button>
            <button onclick="zoomOut('frontend-diagram')">Zoom Out</button>
            <button onclick="resetZoom('frontend-diagram')">Reset</button>
        </div>
        <div id="frontend-diagram" class="mermaid">
            graph TD
                subgraph "Frontend Application"
                    App[App.jsx] --> Router[React Router]
                    Router --> Routes[Routes Configuration]
                    
                    Routes --> LayoutComponent[Layout Component]
                    
                    LayoutComponent --> NavbarComponent[Navbar Component]
                    LayoutComponent --> SidebarComponent[Sidebar Component]
                    LayoutComponent --> ContentArea[Content Area]
                    
                    ContentArea --> DashboardPage[Dashboard Page]
                    ContentArea --> ProjectsPage[Projects Page]
                    ContentArea --> TasksPage[Tasks Page]
                    ContentArea --> LLMPage[LLM Interface Page]
                    ContentArea --> VisualizationPage[Visualization Page]
                    ContentArea --> SettingsPage[Settings Page]
                    
                    %% Dashboard Components
                    DashboardPage --> ProjectStatsComponent[Project Stats Component]
                    DashboardPage --> RecentProjectsComponent[Recent Projects Component]
                    DashboardPage --> QuickActionsComponent[Quick Actions Component]
                    
                    %% Projects Components
                    ProjectsPage --> ProjectListComponent[Project List Component]
                    ProjectsPage --> ProjectFormComponent[Project Form Component]
                    ProjectsPage --> DirectorySelectorComponent[Directory Selector Component]
                    
                    %% Services
                    Services[Services] --> APIService[API Service]
                    Services --> AuthService[Auth Service]
                    Services --> StorageService[Storage Service]
                    
                    %% State Management
                    StateManagement[State Management] --> ReactHooks[React Hooks]
                    
                    %% Styling
                    Styling[Styling] --> TailwindCSS[Tailwind CSS]
                end
                
                classDef component fill:#42a5f5,stroke:#1976d2,color:white;
                classDef page fill:#7e57c2,stroke:#4527a0,color:white;
                classDef service fill:#26a69a,stroke:#00796b,color:white;
                classDef tool fill:#ffa726,stroke:#ef6c00,color:white;
                
                class App,Router,Routes,LayoutComponent,NavbarComponent,SidebarComponent,ContentArea,ProjectStatsComponent,RecentProjectsComponent,QuickActionsComponent,ProjectListComponent,ProjectFormComponent,DirectorySelectorComponent component;
                class DashboardPage,ProjectsPage,TasksPage,LLMPage,VisualizationPage,SettingsPage page;
                class APIService,AuthService,StorageService service;
                class ReactHooks,TailwindCSS tool;
        </div>
    </div>

    <div id="backend" class="diagram-container">
        <div class="description">
            <h2>Backend Architecture</h2>
            <p>This diagram shows the detailed structure of the API and backend services, including endpoints, services, models, and storage.</p>
        </div>
        <div class="zoom-controls">
            <button onclick="zoomIn('backend-diagram')">Zoom In</button>
            <button onclick="zoomOut('backend-diagram')">Zoom Out</button>
            <button onclick="resetZoom('backend-diagram')">Reset</button>
        </div>
        <div id="backend-diagram" class="mermaid">
            graph TD
                subgraph "API Layer (FastAPI)"
                    FastAPI[FastAPI Application] --> APIRouter[API Router]
                    FastAPI --> Middleware[Middleware]
                    
                    Middleware --> CORSMiddleware[CORS Middleware]
                    Middleware --> AuthMiddleware[Authentication Middleware]
                    
                    APIRouter --> ProjectsRouter[Projects Router]
                    APIRouter --> TasksRouter[Tasks Router]
                    APIRouter --> LLMRouter[LLM Router]
                    APIRouter --> VisualizationRouter[Visualization Router]
                    APIRouter --> SettingsRouter[Settings Router]
                    
                    %% Projects Endpoints
                    ProjectsRouter --> GetProjectsEndpoint[GET /projects]
                    ProjectsRouter --> GetProjectEndpoint[GET /projects/{id}]
                    ProjectsRouter --> CreateProjectEndpoint[POST /projects]
                    
                    %% Tasks Endpoints
                    TasksRouter --> GetTasksEndpoint[GET /projects/{id}/tasks]
                    TasksRouter --> GetTaskEndpoint[GET /projects/{id}/tasks/{task_id}]
                    TasksRouter --> CreateTaskEndpoint[POST /projects/{id}/tasks]
                end
                
                subgraph "Backend Services"
                    %% Project Services
                    ProjectService[Project Service] --> ProjectRepository[Project Repository]
                    ProjectService --> DirectoryScanner[Directory Scanner]
                    
                    %% Task Services
                    TaskService[Task Service] --> TaskRepository[Task Repository]
                    
                    %% LLM Services
                    LLMService[LLM Service] --> OpenAIProvider[OpenAI Provider]
                    LLMService --> AnthropicProvider[Anthropic Provider]
                    
                    %% Visualization Services
                    VisualizationService[Visualization Service] --> GraphGenerator[Graph Generator]
                    
                    %% Settings Services
                    SettingsService[Settings Service] --> ConfigRepository[Config Repository]
                    
                    %% Shared Services
                    FileSystemService[File System Service]
                end
                
                subgraph "Storage Layer"
                    %% File System Storage
                    FileSystemStorage[File System Storage] --> VibeArchFolder[.VibeArch Folder]
                    
                    VibeArchFolder --> DirectoryFolder[Directory Folder]
                    VibeArchFolder --> SpecificationFolder[Specification Folder]
                    VibeArchFolder --> ArchitectureFolder[Architecture Folder]
                    VibeArchFolder --> TechStackFolder[TechStack Folder]
                    VibeArchFolder --> DocumentationFolder[Documentation Folder]
                end
                
                %% Connections between layers
                GetProjectsEndpoint --> ProjectService
                GetProjectEndpoint --> ProjectService
                CreateProjectEndpoint --> ProjectService
                
                GetTasksEndpoint --> TaskService
                GetTaskEndpoint --> TaskService
                CreateTaskEndpoint --> TaskService
                
                ProjectService --> FileSystemService
                TaskService --> FileSystemService
                SettingsService --> FileSystemService
                
                FileSystemService --> FileSystemStorage
                
                classDef api fill:#42a5f5,stroke:#1976d2,color:white;
                classDef service fill:#7e57c2,stroke:#4527a0,color:white;
                classDef storage fill:#ef5350,stroke:#c62828,color:white;
                
                class FastAPI,APIRouter,Middleware,CORSMiddleware,AuthMiddleware,ProjectsRouter,TasksRouter,LLMRouter,VisualizationRouter,SettingsRouter,GetProjectsEndpoint,GetProjectEndpoint,CreateProjectEndpoint,GetTasksEndpoint,GetTaskEndpoint,CreateTaskEndpoint api;
                class ProjectService,ProjectRepository,DirectoryScanner,TaskService,TaskRepository,LLMService,OpenAIProvider,AnthropicProvider,VisualizationService,GraphGenerator,SettingsService,ConfigRepository,FileSystemService service;
                class FileSystemStorage,VibeArchFolder,DirectoryFolder,SpecificationFolder,ArchitectureFolder,TechStackFolder,DocumentationFolder storage;
        </div>
    </div>

    <div id="dataflow" class="diagram-container">
        <div class="description">
            <h2>Data Flow</h2>
            <p>This diagram shows how data flows through the system, including user interactions, API requests, and file operations.</p>
        </div>
        <div class="zoom-controls">
            <button onclick="zoomIn('dataflow-diagram')">Zoom In</button>
            <button onclick="zoomOut('dataflow-diagram')">Zoom Out</button>
            <button onclick="resetZoom('dataflow-diagram')">Reset</button>
        </div>
        <div id="dataflow-diagram" class="mermaid">
            flowchart TD
                User([User]) -->|Interacts with| UI[User Interface]
                
                subgraph "Frontend"
                    UI -->|Displays| ProjectsView[Projects View]
                    UI -->|Displays| TasksView[Tasks View]
                    
                    ProjectsView -->|User Action| ProjectsController[Projects Controller]
                    TasksView -->|User Action| TasksController[Tasks Controller]
                    
                    ProjectsController -->|Updates| ProjectsView
                    TasksController -->|Updates| TasksView
                    
                    ProjectsController -->|API Request| APIClient[API Client]
                    TasksController -->|API Request| APIClient
                    
                    APIClient -->|Response Data| ProjectsController
                    APIClient -->|Response Data| TasksController
                end
                
                APIClient -->|HTTP Request| APIGateway[API Gateway]
                APIGateway -->|HTTP Response| APIClient
                
                subgraph "Backend"
                    APIGateway -->|Routes Request| ProjectsAPI[Projects API]
                    APIGateway -->|Routes Request| TasksAPI[Tasks API]
                    
                    ProjectsAPI -->|Response| APIGateway
                    TasksAPI -->|Response| APIGateway
                    
                    ProjectsAPI -->|Processes Request| ProjectsService[Projects Service]
                    TasksAPI -->|Processes Request| TasksService[Tasks Service]
                    
                    ProjectsService -->|Returns Data| ProjectsAPI
                    TasksService -->|Returns Data| TasksAPI
                    
                    ProjectsService -->|Reads/Writes| FileSystemService[File System Service]
                    TasksService -->|Reads/Writes| FileSystemService
                end
                
                FileSystemService -->|File Operations| Storage[File System Storage]
                
                subgraph "Storage"
                    Storage -->|Stores| VibeArchFolder[.VibeArch Folder]
                    
                    VibeArchFolder -->|Contains| DirectoryData[Directory Data]
                    VibeArchFolder -->|Contains| SpecificationData[Specification Data]
                    VibeArchFolder -->|Contains| ArchitectureData[Architecture Data]
                    VibeArchFolder -->|Contains| TechStackData[TechStack Data]
                    VibeArchFolder -->|Contains| DocumentationData[Documentation Data]
                end
                
                %% Data Flow for Project Creation
                User -->|1. Creates Project| UI
                UI -->|2. Submits Form| ProjectsController
                ProjectsController -->|3. Sends Request| APIClient
                APIClient -->|4. HTTP POST| APIGateway
                APIGateway -->|5. Routes to| ProjectsAPI
                ProjectsAPI -->|6. Processes| ProjectsService
                ProjectsService -->|7. Creates Files| FileSystemService
                FileSystemService -->|8. Writes to| Storage
                
                classDef frontend fill:#42a5f5,stroke:#1976d2,color:white;
                classDef api fill:#7e57c2,stroke:#4527a0,color:white;
                classDef service fill:#26a69a,stroke:#00796b,color:white;
                classDef storage fill:#ef5350,stroke:#c62828,color:white;
                classDef user fill:#78909c,stroke:#455a64,color:white;
                
                class UI,ProjectsView,TasksView,ProjectsController,TasksController,APIClient frontend;
                class APIGateway,ProjectsAPI,TasksAPI api;
                class ProjectsService,TasksService,FileSystemService service;
                class Storage,VibeArchFolder,DirectoryData,SpecificationData,ArchitectureData,TechStackData,DocumentationData storage;
                class User user;
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                useMaxWidth: false,
                htmlLabels: true
            }
        });

        // Tab switching
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and containers
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.diagram-container').forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding container
                tab.classList.add('active');
                document.getElementById(tab.dataset.target).classList.add('active');
            });
        });

        // Zoom functionality
        let zoomLevels = {};

        function zoomIn(diagramId) {
            if (!zoomLevels[diagramId]) zoomLevels[diagramId] = 1;
            zoomLevels[diagramId] *= 1.2;
            applyZoom(diagramId);
        }

        function zoomOut(diagramId) {
            if (!zoomLevels[diagramId]) zoomLevels[diagramId] = 1;
            zoomLevels[diagramId] *= 0.8;
            applyZoom(diagramId);
        }

        function resetZoom(diagramId) {
            zoomLevels[diagramId] = 1;
            applyZoom(diagramId);
        }

        function applyZoom(diagramId) {
            const diagram = document.getElementById(diagramId);
            const svg = diagram.querySelector('svg');
            if (svg) {
                svg.style.transform = `scale(${zoomLevels[diagramId]})`;
                svg.style.transformOrigin = 'top left';
            }
        }
    </script>
</body>
</html>
