codebase:
- file: augment_mvcd_helper.py
  element: load_prompt
  type: Other
  description: "Loads the content of a specified prompt file for the MVCD enrichment process."
  confidence: 95
  status: active
  dependencies:
  - argparse
  - pyperclip
  - subprocess
  - threading
  - traceback
  - yaml
  loc: 271
  last_modified: 1747746055.7223716
- file: backend/app/api/endpoints/architecture.py
  element: get_file_hash
  type: Other
  description: "Calculates the MD5 hash of a file to detect if its content has changed."
  confidence: 100
  status: active
  dependencies:
  - fastapi
  - hashlib
  loc: 68
  last_modified: **********.753634
- file: backend/app/api/endpoints/graph.py
  element: get_graph_data
  type: Other
  description: "API endpoint to generate and return graph data representing project tasks and their dependencies for visualization."
  confidence: 90
  status: active
  dependencies:
  - fastapi
  - tempfile
  loc: 35
  last_modified: **********.981479
- file: backend/app/api/endpoints/llm.py
  element: list_providers
  type: Other
  description: "API endpoint to list the LLM providers available for use in the system."
  confidence: 95
  status: active
  dependencies:
  - fastapi
  loc: 32
  last_modified: **********.9774485
- file: backend/app/api/endpoints/mvcd.py
  element: MVCDStatusResponse
  type: Other
  description: "Pydantic model defining the structure for the response containing MVCD status metrics, including entry counts, confidence, and lines of code."
  confidence: 95
  status: active
  dependencies:
  - fastapi
  - pydantic
  - pyperclip
  - subprocess
  - threading
  loc: 531
  last_modified: **********.5749183
- file: backend/app/api/endpoints/projects.py
  element: list_projects
  type: Other
  description: "API endpoint to list all Vibe Architect projects found within a specified base directory."
  confidence: 95
  status: active
  dependencies:
  - fastapi
  - pydantic
  loc: 80
  last_modified: **********.0239427
- file: backend/app/api/endpoints/tasks.py
  element: list_tasks
  type: Other
  description: "API endpoint to list tasks for a specific project, with optional filtering by task status."
  confidence: 95
  status: active
  dependencies:
  - fastapi
  loc: 86
  last_modified: **********.4587839
- file: backend/app/api/router.py
  element: health_check
  type: Other
  description: "API endpoint to check the health status of the Vibe Architect backend API."
  confidence: 100
  status: active
  dependencies:
  - fastapi
  loc: 15
  last_modified: **********.5552886
- file: backend/app/core/config.py
  element: Settings
  type: Other
  description: "Pydantic model defining the application-wide settings for the Vibe Architect backend, loaded from environment variables and a .env file."
  confidence: 95
  status: active
  dependencies:
  - pydantic
  - pydantic_settings
  - yaml
  loc: 41
  last_modified: **********.8720632
- file: backend/app/core/project_manager.py
  element: ProjectManager
  type: Other
  description: "Manages the lifecycle of Vibe Architect projects, including initialization, loading, listing, and updating project settings."
  confidence: 95
  status: active
  dependencies:
  - shutil
  loc: 97
  last_modified: **********.2727323
- file: backend/app/main.py
  element: health_check
  type: Other
  description: "Root API endpoint to provide a basic health check for the Vibe Architect backend service."
  confidence: 100
  status: active
  dependencies:
  - fastapi
  - uvicorn
  loc: 41
  last_modified: **********.747401
- file: backend/app/models/project.py
  element: Project
  type: Other
  description: "Pydantic model representing the structure and data associated with a Vibe Architect project."
  confidence: 95
  status: active
  dependencies:
  - pydantic
  loc: 9
  last_modified: **********.5967286
- file: backend/app/models/task.py
  element: TaskStatus
  type: Other
  description: "Enum defining the possible statuses for a project task (e.g., todo, done)."
  confidence: 100
  status: active
  dependencies:
  - pydantic
  loc: 12
  last_modified: **********.268391
- file: backend/app/routers/directory.py
  element: list_directories
  type: Other
  description: "API endpoint to list the contents (files and directories) of a specified directory path."
  confidence: 95
  status: active
  dependencies:
  - fastapi
  - logging
  loc: 29
  last_modified: **********.3949704
- file: backend/app/schemas/llm.py
  element: LLMProvider
  type: Other
  description: "Pydantic schema defining the structure for representing an LLM provider, including its ID, name, and available models."
  confidence: 95
  status: active
  dependencies:
  - pydantic
  loc: 13
  last_modified: **********.701626
- file: backend/app/schemas/project.py
  element: ProjectBase
  type: Other
  description: "Base Pydantic schema providing common fields for project-related operations."
  confidence: 95
  status: active
  dependencies:
  - pydantic
  loc: 21
  last_modified: **********.2858427
- file: backend/app/schemas/project_check.py
  element: ProjectCheckRequest
  type: Other
  description: "Pydantic schema for the request body used to check if a given directory path is a Vibe Architect project."
  confidence: 95
  status: active
  dependencies:
  - pydantic
  loc: 9
  last_modified: **********.5844054
- file: backend/app/schemas/task.py
  element: TaskBase
  type: Other
  description: "Base Pydantic schema providing common fields for task-related operations."
  confidence: 95
  status: active
  dependencies:
  - pydantic
  loc: 19
  last_modified: 1746701159.3351848
- file: backend/app/services/directory_service.py
  element: DirectoryService
  type: Utility
  description: "Provides static methods for performing directory-related operations, such as listing directories and files within a given path."
  confidence: 95
  status: active
  dependencies:
  - logging
  loc: 41
  last_modified: **********.392154
- file: backend/app/services/file_service.py
  element: FileService
  type: Utility
  description: "Handles reading from and writing to various project-specific files within the .vibearchitect directory, such as settings, specification, and tasks."
  confidence: 95
  status: active
  dependencies:
  - yaml
  loc: 82
  last_modified: **********.5235724
- file: backend/app/services/graph_service.py
  element: GraphService
  type: Utility
  description: "Provides services for generating graph data and visualizations of project tasks and their dependencies."
  confidence: 90
  status: active
  dependencies:
  - matplotlib
  - networkx
  loc: 74
  last_modified: **********.6453714
- file: backend/app/services/llm_service.py
  element: LLMService
  type: Utility
  description: "Manages interactions with configured LLM providers, including listing available providers and sending prompts for processing."
  confidence: 95
  status: active
  dependencies: []
  loc: 43
  last_modified: **********.088758
- file: backend/app/services/mvcd_enrichment.py
  element: MVCDEnrichmentService
  type: Type
  description: "Service responsible for enriching the MVCD data with additional information, potentially using coding agents or other methods."
  confidence: 85
  status: active
  dependencies:
  - argparse
  - logging
  - pyperclip
  - webbrowser
  - yaml
  loc: 223
  last_modified: **********.6472058
- file: backend/app/services/mvcd_service.py
  element: MVCDService
  type: Utility
  description: "Service for scanning the codebase and generating structured metadata about code elements, preserving existing descriptions when re-scanning."
  confidence: 85
  status: active
  dependencies:
  - fnmatch
  - yaml
  loc: 239
  last_modified: **********.1204908
- file: backend/app/services/project_check_service.py
  element: ProjectCheckService
  type: Utility
  description: "Provides functionality to check if a given directory path represents a valid Vibe Architect project directory."
  confidence: 95
  status: active
  dependencies: []
  loc: 18
  last_modified: **********.5824113
- file: backend/app/tests/test_architecture_endpoints.py
  element: test_list_diagrams
  type: Other
  description: "Tests the API endpoint that lists available architecture diagrams."
  confidence: 100
  status: active
  dependencies:
  - pytest
  - respx
  - httpx
  loc: 46
  last_modified: **********.7556214
- file: backend/app/tests/test_config.py
  element: test_settings_default
  type: Other
  description: "Tests the default settings loaded by the application's configuration module."
  confidence: 100
  status: active
  dependencies:
  - pytest
  - os
  - sys
  loc: 35
  last_modified: 1746701100.8610387
- file: backend/app/tests/test_directory_endpoints.py
  element: test_list_directories_success
  type: Other
  description: "Tests the API endpoint for listing directories under a valid path, ensuring correct response format and content."
  confidence: 95
  status: active
  dependencies:
  - pytest
  - os
  - fastapi
  - fastapi.testclient
  - backend.app.main
  loc: 73
  last_modified: **********.3985853
- file: backend/app/tests/test_file_service.py
  element: test_read_write_settings
  type: Other
  description: "Tests the FileService's ability to read and write project settings to a YAML file, ensuring data integrity."
  confidence: 95
  status: active
  dependencies:
  - pytest
  - os
  - yaml
  - backend.app.services.file_service
  - backend.app.core.config
  loc: 71
  last_modified: **********.5255685
- file: backend/app/tests/test_graph_endpoints.py
  element: test_get_graph_data
  type: Other
  description: "Tests the API endpoint that provides graph data for project visualization, verifying the structure and content of the generated data."
  confidence: 90
  status: active
  dependencies:
  - pytest
  - respx
  - httpx
  - backend.app.main
  - backend.app.core.project_manager
  - backend.app.services.graph_service
  - backend.app.models.project
  - backend.app.models.task
  loc: 81
  last_modified: **********.6473646
- file: backend/app/tests/test_llm_endpoints.py
  element: test_list_providers
  type: Other
  description: "Tests the API endpoint that lists available LLM providers, checking for a successful response and the presence of expected data."
  confidence: 95
  status: active
  dependencies:
  - pytest
  - respx
  - httpx
  - backend.app.main
  - backend.app.services.llm_service
  - backend.app.schemas.llm
  loc: 44
  last_modified: **********.0907514
- file: backend/app/tests/test_main.py
  element: test_health_check
  type: Other
  description: "Tests the primary health check endpoint of the backend API to ensure it returns a healthy status."
  confidence: 100
  status: active
  dependencies:
  - pytest
  - fastapi.testclient
  - backend.app.main
  loc: 11
  last_modified: **********.7493937
- file: backend/app/tests/test_mvcd_endpoints.py
  element: test_get_mvcd_status_no_file
  type: Other
  description: "Tests the MVCD status API endpoint when the MVCD file does not exist, verifying that it returns the correct status indicating the file is missing."
  confidence: 95
  status: active
  dependencies:
  - pytest
  - os
  - backend.app.main
  - fastapi.testclient
  - backend.app.services.mvcd_service
  loc: 55
  last_modified: **********.6530392
- file: backend/app/tests/test_project_manager.py
  element: test_initialize_project
  type: Other
  description: "Tests the ProjectManager's ability to initialize a new project directory with the required Vibe Architect structure and default files."
  confidence: 95
  status: active
  dependencies:
  - pytest
  - os
  - shutil
  - backend.app.core.project_manager
  - backend.app.core.config
  loc: 124
  last_modified: **********.2747254
- file: backend/app/tests/test_projects_endpoints.py
  element: test_list_projects_empty
  type: Other
  description: "Tests the API endpoint for listing projects when the specified base directory contains no Vibe Architect projects."
  confidence: 95
  status: active
  dependencies:
  - pytest
  - respx
  - httpx
  - backend.app.main
  - backend.app.core.project_manager
  loc: 33
  last_modified: **********.0259357
- file: backend/app/tests/test_task_endpoints.py
  element: test_list_tasks_empty
  type: Other
  description: "Tests the API endpoint for listing tasks when a project has no tasks, ensuring an empty list is returned."
  confidence: 95
  status: active
  dependencies:
  - pytest
  - respx
  - httpx
  - backend.app.main
  - backend.app.core.project_manager
  - backend.app.models.project
  loc: 50
  last_modified: **********.4607766
- file: augment_mvcd_helper.py
  element: save_response
  type: Other
  description: "Saves the content from the clipboard to a specified file, validating if it appears to be a valid MVCD YAML response."
  confidence: 95
  status: active
  dependencies:
  - os
  - sys
  - time
  - argparse
  - yaml
  - subprocess
  - threading
  - traceback
  - pathlib
  - pyperclip
  loc: 41
  last_modified: 1747746055.7243645
- file: augment_mvcd_helper.py
  element: monitor_file_changes
  type: Other
  description: "Monitors a specified file for changes over time, providing updates and checking if the file content is a complete MVCD YAML."
  confidence: 90
  status: active
  dependencies:
  - os
  - sys
  - time
  - argparse
  - yaml
  - subprocess
  - threading
  - traceback
  - pathlib
  - pyperclip
  loc: 69
  last_modified: 1747746055.726358
- file: augment_mvcd_helper.py
  element: is_autohotkey_installed
  type: Other
  description: "Checks if AutoHotKey is installed on the system, bypassing actual detection based on user confirmation."
  confidence: 80
  status: active
  dependencies:
  - os
  - sys
  - time
  - argparse
  - yaml
  - subprocess
  - threading
  - traceback
  - pathlib
  - pyperclip
  loc: 126
  last_modified: 1747746055.7283528
- file: augment_mvcd_helper.py
  element: get_autohotkey_path
  type: Other
  description: "Retrieves the executable path for AutoHotKey, assuming it's in the system's PATH."
  confidence: 80
  status: active
  dependencies:
  - os
  - sys
  - time
  - argparse
  - yaml
  - subprocess
  - threading
  - traceback
  - pathlib
  - pyperclip
  loc: 173
  last_modified: 1747746055.7303464
- file: augment_mvcd_helper.py
  element: run_autohotkey_script
  type: Other
  description: "Runs a specified AutoHotKey script using the AutoHotKey executable."
  confidence: 90
  status: active
  dependencies:
  - os
  - sys
  - time
  - argparse
  - yaml
  - subprocess
  - threading
  - traceback
  - pathlib
  - pyperclip
  loc: 222
  last_modified: 1747746055.7323427
- file: augment_mvcd_helper.py
  element: main
  type: Other
  description: "Main function to parse arguments and run the MVCD augmentation helper script in manual or automated mode."
  confidence: 90
  status: active
  dependencies:
  - os
  - sys
  - time
  - argparse
  - yaml
  - subprocess
  - threading
  - traceback
  - pathlib
  - pyperclip
  loc: 329
  last_modified: 1747746055.734339
- file: backend/app/api/endpoints/mvcd.py
  element: get_mvcd_path
  type: Other
  description: "Helper function to construct the file system path to the mvcd.yaml file for a given project path."
  confidence: 100
  status: active
  dependencies:
  - os
  - sys
  - json
  - time
  - threading
  - typing
  - pathlib
  - fastapi
  - fastapi.responses
  - pydantic
  - pyperclip
  - backend.app.services.mvcd_service
  - backend.app.services.mvcd_enrichment
  loc: 60
  last_modified: **********.5779014
- file: backend/app/api/endpoints/mvcd.py
  element: calculate_mvcd_metrics
  type: Other
  description: "Calculates various metrics from the MVCD file, such as total entries, entries with descriptions, and average confidence scores."
  confidence: 95
  status: active
  dependencies:
  - os
  - sys
  - json
  - time
  - threading
  - typing
  - pathlib
  - fastapi
  - fastapi.responses
  - pydantic
  - pyperclip
  - backend.app.services.mvcd_service
  - backend.app.services.mvcd_enrichment
  loc: 65
  last_modified: **********.579897
- file: backend/app/api/endpoints/mvcd.py
  element: get_mvcd_status
  type: Other
  description: "API endpoint to retrieve the current status and metrics of the MVCD for a specified project."
  confidence: 95
  status: active
  dependencies:
  - os
  - sys
  - json
  - time
  - threading
  - typing
  - pathlib
  - fastapi
  - fastapi.responses
  - pydantic
  - pyperclip
  - backend.app.services.mvcd_service
  - backend.app.services.mvcd_enrichment
  loc: 149
  last_modified: **********.5818932
- file: backend/app/api/endpoints/mvcd.py
  element: get_mvcd_content
  type: Other
  description: "API endpoint to retrieve the raw content of the mvcd.yaml file for a specified project."
  confidence: 95
  status: active
  dependencies:
  - os
  - sys
  - json
  - time
  - threading
  - typing
  - pathlib
  - fastapi
  - fastapi.responses
  - pydantic
  - pyperclip
  - backend.app.services.mvcd_service
  - backend.app.services.mvcd_enrichment
  loc: 164
  last_modified: **********.5838883
- file: backend/app/api/endpoints/mvcd.py
  element: generate_mvcd
  type: Other
  description: "API endpoint to initiate the background task for generating the initial MVCD structure for a project."
  confidence: 90
  status: active
  dependencies:
  - os
  - sys
  - json
  - time
  - threading
  - typing
  - pathlib
  - fastapi
  - fastapi.responses
  - pydantic
  - pyperclip
  - backend.app.services.mvcd_service
  - backend.app.services.mvcd_enrichment
  loc: 182
  last_modified: **********.5858846
- file: backend/app/api/endpoints/mvcd.py
  element: run_mvcd_generation
  type: Other
  description: "Background task function to execute the MVCD generation process for a project and update the task status."
  confidence: 90
  status: active
  dependencies:
  - os
  - sys
  - json
  - time
  - threading
  - typing
  - pathlib
  - fastapi
  - fastapi.responses
  - pydantic
  - pyperclip
  - backend.app.services.mvcd_service
  - backend.app.services.mvcd_enrichment
  loc: 195
  last_modified: **********.5878809
- file: backend/app/api/endpoints/mvcd.py
  element: enrich_mvcd
  type: Other
  description: "API endpoint to initiate the background task for enriching the MVCD data using a specified coding agent."
  confidence: 90
  status: active
  dependencies:
  - os
  - sys
  - json
  - time
  - threading
  - typing
  - pathlib
  - fastapi
  - fastapi.responses
  - pydantic
  - pyperclip
  - backend.app.services.mvcd_service
  - backend.app.services.mvcd_enrichment
  loc: 208
  last_modified: **********.589876
- file: backend/app/api/endpoints/mvcd.py
  element: run_mvcd_enrichment
  type: Other
  description: "Background task function to execute the MVCD enrichment process using the specified coding agent and handle task status updates, including potential user interaction."
  confidence: 85
  status: active
  dependencies:
  - os
  - sys
  - json
  - time
  - threading
  - typing
  - pathlib
  - fastapi
  - fastapi.responses
  - pydantic
  - pyperclip
  - backend.app.services.mvcd_service
  - backend.app.services.mvcd_enrichment
  - subprocess
  loc: 211
  last_modified: **********.5918722
- file: backend/app/services/mvcd_service.py
  element: load_ignore_file
  type: Other
  description: "Loads file and directory ignore patterns from a .mvcd-ignore.yaml file located within the .VibeArch directory."
  confidence: 95
  status: active
  dependencies:
  - os
  - re
  - yaml
  - fnmatch
  - pathlib
  - typing
  loc: 41
  last_modified: **********.1224847
- file: backend/app/services/mvcd_service.py
  element: should_ignore
  type: Other
  description: "Checks if a given file or directory path should be ignored based on the loaded ignore patterns."
  confidence: 95
  status: active
  dependencies:
  - os
  - re
  - yaml
  - fnmatch
  - pathlib
  - typing
  loc: 87
  last_modified: **********.12448
- file: backend/app/services/mvcd_service.py
  element: load_existing_mvcd
  type: Other
  description: "Loads existing MVCD data from the mvcd.yaml file if it exists."
  confidence: 95
  status: active
  dependencies:
  - os
  - re
  - yaml
  - fnmatch
  - pathlib
  - typing
  loc: 119
  last_modified: **********.1264756
- file: backend/app/services/mvcd_service.py
  element: count_lines_of_code
  type: Other
  description: "Counts the number of non-blank, non-comment lines of code in a given file, supporting multiple languages."
  confidence: 90
  status: active
  dependencies:
  - os
  - re
  - yaml
  - fnmatch
  - pathlib
  - typing
  loc: 137
  last_modified: **********.1284711
- file: backend/app/services/mvcd_service.py
  element: detect_dependencies
  type: Other
  description: "Analyzes a file's content to detect and list external dependencies being imported."
  confidence: 85
  status: active
  dependencies:
  - os
  - re
  - yaml
  - fnmatch
  - pathlib
  - typing
  loc: 172
  last_modified: **********.1304672
- file: backend/app/services/mvcd_service.py
  element: detect_element_type
  type: Other
  description: "Determines the type of the main code element within a file based on content and naming conventions (e.g., Component, Hook, Utility)."
  confidence: 80
  status: active
  dependencies:
  - os
  - re
  - yaml
  - fnmatch
  - pathlib
  - typing
  loc: 222
  last_modified: **********.1324623
- file: backend/app/services/mvcd_service.py
  element: detect_main_element
  type: Other
  description: "Identifies the name of the primary exported code element (class, function, component) within a file."
  confidence: 85
  status: active
  dependencies:
  - os
  - re
  - yaml
  - fnmatch
  - pathlib
  - typing
  loc: 264
  last_modified: **********.134458
- file: backend/app/services/mvcd_service.py
  element: scan_codebase
  type: Other
  description: "Recursively traverses the project directory, processes relevant code files, and generates a list of MVCD entries."
  confidence: 90
  status: active
  dependencies:
  - os
  - re
  - yaml
  - fnmatch
  - pathlib
  - typing
  loc: 304
  last_modified: **********.1364539
- file: backend/app/services/mvcd_service.py
  element: merge_with_existing
  type: Other
  description: "Merges newly scanned MVCD entries with existing data, preserving previous descriptions, confidence scores, and status where available."
  confidence: 95
  status: active
  dependencies:
  - os
  - re
  - yaml
  - fnmatch
  - pathlib
  - typing
  loc: 375
  last_modified: **********.138449
- file: backend/app/services/mvcd_service.py
  element: save_mvcd
  type: Other
  description: "Saves the provided list of MVCD entries to the mvcd.yaml file in a structured format."
  confidence: 95
  status: active
  dependencies:
  - os
  - re
  - yaml
  - fnmatch
  - pathlib
  - typing
  loc: 444
  last_modified: **********.140445
- file: backend/app/services/mvcd_service.py
  element: generate
  type: Other
  description: "Orchestrates the MVCD generation process by loading existing data, scanning the codebase, merging the results, and saving the updated MVCD file."
  confidence: 90
  status: active
  dependencies:
  - os
  - re
  - yaml
  - fnmatch
  - pathlib
  - typing
  loc: 468
  last_modified: **********.1424413
- file: backend/middleware/static_files.py
  element: VibeArchStaticFiles
  type: Other
  description: "Custom FastAPI StaticFiles class that extends functionality to serve static files from a project's .VibeArch directory, potentially overriding default files."
  confidence: 90
  status: active
  dependencies:
  - fastapi
  loc: 39
  last_modified: 1747685009.234377
- file: backend/setup.py
  element: setup
  type: Other
  description: "Setuptools configuration for the Vibe Architect backend package, defining its name, version, packages, and dependencies."
  confidence: 95
  status: active
  dependencies:
  - setuptools
  loc: 19
  last_modified: **********.1099174
- file: enrich_mvcd.py
  element: enrich_mvcd
  type: Other
  description: "The main entry point script to trigger the MVCD enrichment process using a coding agent."
  confidence: 90
  status: active
  dependencies: []
  loc: 6
  last_modified: **********.7567697
- file: frontend-vite/src/App.jsx
  element: App
  type: Component
  description: "The main application component that sets up routing and provides contexts for project management, coding agent selection, and notifications."
  confidence: 95
  status: active
  dependencies:
  - react
  - react-router-dom
  loc: 105
  last_modified: **********.41502
- file: frontend-vite/src/EnvTest.jsx
  element: EnvTest
  type: Component
  description: "A component used for testing and displaying non-sensitive environment variables during development."
  confidence: 90
  status: active
  dependencies:
  - react
  loc: 25
  last_modified: **********.204539
- file: frontend-vite/src/components/ApiTest.jsx
  element: ApiTest
  type: Component
  description: "A frontend component for manually testing the backend API endpoints, such as health checks and project checks."
  confidence: 90
  status: active
  dependencies:
  - axios
  - react
  loc: 121
  last_modified: **********.554899
- file: frontend-vite/src/components/AppLayout.jsx
  element: AppLayout
  type: Component
  description: "Provides a consistent layout structure for the main application content, including the application header."
  confidence: 95
  status: active
  dependencies:
  - react
  loc: 13
  last_modified: **********.0654404
- file: frontend-vite/src/components/Architecture.jsx
  element: Architecture
  type: Component
  description: "A frontend component that displays architecture diagrams loaded from the project's .VibeArch directory or public assets, with tab navigation and zoom functionality."
  confidence: 90
  status: active
  dependencies:
  - mermaid
  - react
  loc: 292
  last_modified: **********.6878846
- file: frontend-vite/src/components/ClearStorage.jsx
  element: ClearStorage
  type: Component
  description: "A component that provides a user interface to clear project-related data stored in the browser's local storage."
  confidence: 95
  status: active
  dependencies:
  - react
  loc: 55
  last_modified: 1747685448.4288437
- file: frontend-vite/src/components/Dashboard.jsx
  element: Dashboard
  type: Component
  description: "The main dashboard component displaying project statistics, recent projects, and allowing the user to select or scan for a project directory."
  confidence: 90
  status: active
  dependencies:
  - react
  - react-router-dom
  loc: 285
  last_modified: 1747689842.4372528
- file: frontend-vite/src/components/Directory.jsx
  element: Directory
  type: Component
  description: "A frontend component that visualizes the file and directory structure of the currently selected project."
  confidence: 90
  status: active
  dependencies:
  - react
  loc: 142
  last_modified: 1747687333.9284453
- file: frontend-vite/src/components/FileBrowser.jsx
  element: FileBrowser
  type: Component
  description: "A modal component providing a file browsing interface to select directories, interacting with the backend API to list directory contents."
  confidence: 90
  status: active
  dependencies:
  - react
  loc: 158
  last_modified: 1747686985.7885532
- file: frontend-vite/src/components/Layout.jsx
  element: Layout
  type: Component
  description: "Provides the main application layout, including a sidebar navigation and a status indicator for the backend API connection."
  confidence: 95
  status: active
  dependencies:
  - react
  loc: 59
  last_modified: 1747746664.66801
- file: frontend-vite/src/components/MVCD.jsx
  element: MVCD
  type: Component
  description: "A frontend component for interacting with the MVCD generation and enrichment workflow, displaying MVCD status, triggering tasks, and visualizing the codebase structure based on MVCD data."
  confidence: 85
  status: active
  dependencies:
  - js-yaml
  - react
  loc: 785
  last_modified: 1747750965.219604
- file: frontend-vite/src/components/Notification.jsx
  element: Notification
  type: Component
  description: "A reusable component for displaying transient or permanent notification messages to the user based on type (info, success, warning, error)."
  confidence: 95
  status: active
  dependencies:
  - react
  loc: 102
  last_modified: 1747690157.3626318
- file: frontend-vite/src/components/ProjectSelection.jsx
  element: ProjectSelection
  type: Component
  description: "A component displayed when no project is selected, allowing users to select an existing project directory or initialize a new one using a file browser interface."
  confidence: 90
  status: active
  dependencies:
  - react
  loc: 318
  last_modified: 1747685704.1265574
- file: frontend-vite/src/components/Projects.jsx
  element: Projects
  type: Component
  description: "A frontend page component that lists recent Vibe Architect projects, allows scanning for new projects, and provides an interface to create a new project."
  confidence: 90
  status: active
  dependencies:
  - react
  loc: 302
  last_modified: 1747690264.4857745
- file: frontend-vite/src/components/common/AppHeader.jsx
  element: AppHeader
  type: Component
  description: "The application header component displayed at the top of the main layout, showing the Vibe Architect logo and title."
  confidence: 100
  status: active
  dependencies:
  - react
  - react-router-dom
  loc: 18
  last_modified: 1747655021.9440346
- file: frontend-vite/src/components/common/HeaderTest.jsx
  element: HeaderTest
  type: Component
  description: "A simple test header component used for development purposes."
  confidence: 100
  status: active
  dependencies:
  - react
  loc: 9
  last_modified: 1747649145.7030718
- file: frontend-vite/src/components/common/Navbar.jsx
  element: Navbar
  type: Component
  description: "A navigation bar component providing links to different sections of the Vibe Architect application."
  confidence: 95
  status: active
  dependencies:
  - react
  - react-router-dom
  loc: 156
  last_modified: 1747655072.0684154
- file: frontend-vite/src/components/common/Sidebar.jsx
  element: Sidebar
  type: Component
  description: "A sidebar navigation component providing links to different sections of the application, indicating the currently active route."
  confidence: 95
  status: active
  dependencies:
  - react-router-dom
  loc: 120
  last_modified: 1747656135.4305513
- file: frontend-vite/src/contexts/CodingAgentContext.jsx
  element: CodingAgentContext
  type: Context
  description: "React context providing access to the currently selected coding agent preference, stored in local storage."
  confidence: 95
  status: active
  dependencies:
  - react
  loc: 25
  last_modified: 1747689669.7245343
- file: frontend-vite/src/contexts/NotificationContext.jsx
  element: NotificationContext
  type: Context
  description: "React context for managing and displaying application-wide notifications."
  confidence: 95
  status: active
  dependencies:
  - react
  loc: 65
  last_modified: 1747690176.6664555
- file: frontend-vite/src/contexts/ProjectContext.jsx
  element: ProjectContext
  type: Context
  description: "React context for managing the current and recent Vibe Architect projects, including loading, checking, and initializing projects."
  confidence: 95
  status: active
  dependencies:
  - react
  loc: 222
  last_modified: 1747685704.1245625
- file: frontend-vite/src/hooks/useProject.jsx
  element: useProject
  type: Hook
  description: "A custom React hook that provides convenient access to the ProjectContext for managing project-related state and actions."
  confidence: 95
  status: active
  dependencies:
  - react
  loc: 9
  last_modified: **********.5854027
- file: frontend-vite/src/main.jsx
  element: App
  type: Other
  description: "The main entry point for the Vibe Architect frontend application, rendering the root App component within React's StrictMode."
  confidence: 95
  status: active
  dependencies:
  - react
  - react-dom
  loc: 11
  last_modified: 1746701068.9479468
- file: frontend-vite/src/services/api.js
  element: projectApi
  type: Utility
  description: "An object containing functions for making API calls related to project management endpoints."
  confidence: 90
  status: active
  dependencies:
  - axios
  loc: 14
  last_modified: **********.5893936
- file: frontend-vite/src/services/api.js
  element: directoryApi
  type: Utility
  description: "An object containing functions for making API calls related to directory listing endpoints."
  confidence: 90
  status: active
  dependencies:
  - axios
  loc: 23
  last_modified: **********.59139
- file: frontend-vite/src/services/api.js
  element: mvcdApi
  type: Utility
  description: "An object containing functions for making API calls related to MVCD (Minimum Viable Code Description) endpoints."
  confidence: 90
  status: active
  dependencies:
  - axios
  loc: 31
  last_modified: **********.5933862
- file: frontend-vite/src/services/api.js
  element: llmApi
  type: Utility
  description: "An object containing functions for making API calls related to LLM (Large Language Model) endpoints."
  confidence: 90
  status: active
  dependencies:
  - axios
  loc: 43
  last_modified: **********.595383
- file: frontend-vite/src/services/api.js
  element: architectureApi
  type: Utility
  description: "An object containing functions for making API calls related to architecture diagram endpoints."
  confidence: 90
  status: active
  dependencies:
  - axios
  loc: 52
  last_modified: **********.59738
- file: frontend-vite/src/services/api.js
  element: api
  type: Other
  description: "An Axios instance configured with the base URL for the backend API."
  confidence: 95
  status: active
  dependencies:
  - axios
  loc: 62
  last_modified: **********.5993767
- file: frontend-vite/src/utils/apiUtils.js
  element: isBackendAvailable
  type: Utility
  description: "Checks if the backend API is available by making a request to the health endpoint."
  confidence: 95
  status: active
  dependencies:
  - axios
  loc: 8
  last_modified: **********.6013734
- file: frontend-vite/src/utils/apiUtils.js
  element: handleApiError
  type: Utility
  description: "Handles errors from API calls, logging the error and optionally showing a notification to the user."
  confidence: 90
  status: active
  dependencies:
  - axios
  loc: 24
  last_modified: **********.60337
- file: frontend-vite/src/utils/apiUtils.js
  element: createMinimalProject
  type: Utility
  description: "Creates a minimal project object when the backend is unavailable, containing essential information derived from the project path."
  confidence: 95
  status: active
  dependencies: []
  loc: 41
  last_modified: **********.605367
