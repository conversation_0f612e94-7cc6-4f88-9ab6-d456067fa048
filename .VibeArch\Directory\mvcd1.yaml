codebase:
- file: backend/app/__init__.py
  element: __init__
  type: Other
  description: "Initialization file for the Vibe Architect backend application package, defining the application version."
  confidence: 90
  status: active
  dependencies: []
  loc: 1
- file: backend/app/api/__init__.py
  element: __init__
  type: Other
  description: "Empty initialization file that marks the api directory as a Python package, enabling imports from this package."
  confidence: 85
  status: active
  dependencies: []
  loc: 0
- file: backend/app/api/endpoints/__init__.py
  element: __init__
  type: Other
  description: "Initialization file for the API endpoints package that imports and exposes all endpoint modules for the Vibe Architect application."
  confidence: 95
  status: active
  dependencies: []
  loc: 1
- file: backend/app/api/endpoints/architecture.py
  element: get_file_hash
  type: Other
  description: "Utility function that calculates an MD5 hash of a file's contents to detect changes, used for caching architecture diagrams."
  confidence: 95
  status: active
  dependencies:
  - fastapi
  - hashlib
  loc: 68
- file: backend/app/api/endpoints/architecture.py
  element: router
  type: Other
  description: 'API endpoints for managing architecture diagrams and visualizations for projects.'
  confidence: 90
  status: active
  dependencies:
  - fastapi
  - project_manager
  loc: 40
- file: backend/app/api/endpoints/graph.py
  element: get_graph_data
  type: Other
  description: "API endpoint function that retrieves and processes graph data for visualizing project architecture relationships."
  confidence: 80
  status: active
  dependencies:
  - fastapi
  - tempfile
  loc: 35
- file: backend/app/api/endpoints/graph.py
  element: router
  type: Other
  description: 'API endpoints for generating and retrieving graph data and visualizations of project architecture.'
  confidence: 90
  status: active
  dependencies:
  - fastapi
  - project_manager
  - graph_service
  loc: 50
- file: backend/app/api/endpoints/llm.py
  element: list_providers
  type: Other
  description: "API endpoint function that retrieves and returns a list of available LLM providers and their supported models."
  confidence: 95
  status: active
  dependencies:
  - fastapi
  loc: 32
- file: backend/app/api/endpoints/llm.py
  element: router
  type: Other
  description: 'API endpoints for interacting with LLM providers, sending prompts, and retrieving model information.'
  confidence: 90
  status: active
  dependencies:
  - fastapi
  - llm_service
  loc: 45
- file: backend/app/api/endpoints/mvcd.py
  element: MVCDStatusResponse
  type: Other
  description: "Pydantic model that defines the response structure for MVCD status queries, including metrics like entry counts, confidence scores, and lines of code statistics."
  confidence: 95
  status: active
  dependencies:
  - fastapi
  - pydantic
  loc: 191
- file: backend/app/api/endpoints/projects.py
  element: list_projects
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - fastapi
  - pydantic
  loc: 80
- file: backend/app/api/endpoints/projects.py
  element: router
  type: Other
  description: 'API endpoints for project management, including creating, listing,
    checking, and initializing projects.

    '
  dependencies:
  - fastapi
  - project_manager
  loc: 120
- file: backend/app/api/endpoints/tasks.py
  element: list_tasks
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - fastapi
  loc: 86
- file: backend/app/api/endpoints/tasks.py
  element: router
  type: Other
  description: 'API endpoints for managing project tasks, including creating, updating,
    and listing tasks.

    '
  dependencies:
  - fastapi
  - project_manager
  loc: 60
- file: backend/app/api/router.py
  element: health_check
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - fastapi
  loc: 15
- file: backend/app/api/router.py
  element: router
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - fastapi
  loc: 11
- file: backend/app/api/router.py
  element: api_router
  type: Other
  description: 'Main API router that includes all endpoint routers for projects, tasks,
    graph, LLM, and architecture functionality.

    '
  dependencies:
  - fastapi
  loc: 10
- file: backend/app/core/__init__.py
  element: __init__
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies: []
  loc: 0
- file: backend/app/core/config.py
  element: Settings
  type: Other
  description: 'Configuration settings class for the backend application, handling
    API settings, CORS configuration, LLM settings, and file paths.

    '
  confidence: 70
  dependencies:
  - pydantic
  - pydantic_settings
  - yaml
  loc: 41
- file: backend/app/core/config.py
  element: ProjectSettings
  type: Other
  description: 'Project-specific settings model for managing LLM provider preferences,
    API keys, and project structure configuration.

    '
  dependencies:
  - pydantic
  - yaml
  loc: 25
- file: backend/app/core/project_manager.py
  element: ProjectManager
  type: Other
  description: 'Core service that manages project initialization, loading, and operations
    for Vibe Architect projects.

    '
  confidence: 70
  dependencies:
  - shutil
  loc: 97
- file: backend/app/main.py
  element: health_check
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - fastapi
  - uvicorn
  loc: 41
- file: backend/app/main.py
  element: app
  type: Other
  description: 'Main FastAPI application entry point that configures CORS, mounts
    API routes,  and sets up static file serving for the Vibe Architect backend.

    '
  dependencies:
  - fastapi
  - cors_middleware
  - api_router
  - settings
  loc: 45
- file: backend/app/models/__init__.py
  element: __init__
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies: []
  loc: 0
- file: backend/app/models/project.py
  element: Project
  type: Other
  description: 'Data model representing a Vibe Architect project with its path, settings,
    and associated tasks.

    '
  confidence: 70
  dependencies:
  - pydantic
  loc: 9
- file: backend/app/models/task.py
  element: TaskStatus
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - pydantic
  loc: 12
- file: backend/app/models/task.py
  element: Task
  type: Type
  description: 'Data model representing a project task with status, description, and
    metadata.

    '
  dependencies:
  - pydantic
  loc: 20
- file: backend/app/routers/directory.py
  element: list_directories
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - fastapi
  - logging
  loc: 29
- file: backend/app/schemas/__init__.py
  element: __init__
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies: []
  loc: 0
- file: backend/app/schemas/llm.py
  element: LLMProvider
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - pydantic
  loc: 13
- file: backend/app/schemas/project.py
  element: ProjectBase
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - pydantic
  loc: 21
- file: backend/app/schemas/project.py
  element: ProjectResponse
  type: Type
  description: 'Schema for project API responses, defining the structure of project
    data returned to clients.

    '
  dependencies:
  - pydantic
  loc: 15
- file: backend/app/schemas/project_check.py
  element: ProjectCheckRequest
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - pydantic
  loc: 9
- file: backend/app/schemas/project_check.py
  element: ProjectCheckResponse
  type: Type
  description: 'Schema for project directory check responses, indicating if a directory
    exists and has a .vibearch folder.

    '
  dependencies:
  - pydantic
  loc: 10
- file: backend/app/schemas/task.py
  element: TaskBase
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - pydantic
  loc: 19
- file: backend/app/services/__init__.py
  element: __init__
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies: []
  loc: 0
- file: backend/app/services/directory_service.py
  element: DirectoryService
  type: Utility
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - logging
  loc: 41
- file: backend/app/services/file_service.py
  element: FileService
  type: Utility
  description: 'Service for file system operations, including reading, writing, and
    scanning project directories.

    '
  confidence: 70
  dependencies:
  - yaml
  loc: 82
- file: backend/app/services/graph_service.py
  element: GraphService
  type: Utility
  description: 'Service for generating graph data and visualizations of project architecture.

    '
  confidence: 70
  dependencies:
  - matplotlib
  - networkx
  loc: 74
- file: backend/app/services/llm_service.py
  element: LLMService
  type: Utility
  description: 'Service for interacting with LLM providers, handling API calls and
    response processing.

    '
  confidence: 70
  dependencies: []
  loc: 43
- file: backend/app/services/mvcd_enrichment.py
  element: MVCDEnrichmentService
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - argparse
  - logging
  - yaml
  loc: 201
- file: backend/app/services/mvcd_service.py
  element: MVCDService
  type: Context
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - fnmatch
  - yaml
  loc: 223
- file: backend/middleware/static_files.py
  element: VibeArchStaticFiles
  type: Other
  description: 'Custom static files handler for serving files from .VibeArch directories
    across different projects.

    '
  confidence: 70
  dependencies:
  - fastapi
  loc: 39
- file: backend/setup.py
  element: setup
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - setuptools
  loc: 19
- file: enrich_mvcd.py
  element: enrich_mvcd
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies: []
  loc: 6
- file: frontend-vite/src/App.jsx
  element: App
  type: Component
  description: 'Main application component that sets up routing and project context,
    conditionally rendering project selection or main application.

    '
  confidence: 70
  dependencies:
  - react
  - react-router-dom
  loc: 105
- file: frontend-vite/src/EnvTest.jsx
  element: EnvTest
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  loc: 24
- file: frontend-vite/src/components/ApiTest.jsx
  element: ApiTest
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - axios
  - react
  loc: 121
- file: frontend-vite/src/components/AppLayout.jsx
  element: AppLayout
  type: Component
  description: 'Top-level layout component that wraps the entire application with
    the header and main content area.

    '
  confidence: 70
  dependencies:
  - react
  loc: 13
- file: frontend-vite/src/components/Architecture.jsx
  element: Architecture
  type: Component
  description: 'Component for displaying architecture diagrams with tabs for different
    views and zoom controls.

    '
  confidence: 70
  dependencies:
  - mermaid
  - react
  loc: 292
- file: frontend-vite/src/components/ClearStorage.jsx
  element: ClearStorage
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  loc: 55
- file: frontend-vite/src/components/Dashboard.jsx
  element: Dashboard
  type: Component
  description: 'Dashboard component showing project overview, recent activity, and
    directory selection controls.

    '
  confidence: 70
  dependencies:
  - react
  - react-router-dom
  loc: 285
- file: frontend-vite/src/components/Directory.jsx
  element: Directory
  type: Component
  description: 'Component for displaying the project directory structure as an expandable
    tree.

    '
  confidence: 70
  dependencies:
  - react
  loc: 142
- file: frontend-vite/src/components/FileBrowser.jsx
  element: FileBrowser
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  loc: 158
- file: frontend-vite/src/components/Layout.jsx
  element: Layout
  type: Component
  description: 'Main layout component for authenticated views, including sidebar,
    navbar, and API status indicator.

    '
  confidence: 70
  dependencies:
  - react
  loc: 61
- file: frontend-vite/src/components/MVCD.jsx
  element: MVCD
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  loc: 575
- file: frontend-vite/src/components/Notification.jsx
  element: Notification
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  loc: 102
- file: frontend-vite/src/components/ProjectSelection.jsx
  element: ProjectSelection
  type: Component
  description: 'Component for selecting or creating a project, with recent projects
    list and directory selection.

    '
  confidence: 70
  dependencies:
  - react
  loc: 318
- file: frontend-vite/src/components/Projects.jsx
  element: Projects
  type: Component
  description: 'Component for managing projects, including listing, creating, and
    selecting projects.

    '
  confidence: 70
  dependencies:
  - react
  loc: 302
- file: frontend-vite/src/components/common/AppHeader.jsx
  element: AppHeader
  type: Component
  description: 'Application header component displaying the logo and application title
    with tagline.

    '
  confidence: 70
  dependencies:
  - react
  - react-router-dom
  loc: 18
- file: frontend-vite/src/components/common/HeaderTest.jsx
  element: HeaderTest
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  loc: 9
- file: frontend-vite/src/components/common/Navbar.jsx
  element: Navbar
  type: Component
  description: 'Navigation bar component with links to different sections of the application
    and responsive mobile menu.

    '
  confidence: 70
  dependencies:
  - react
  - react-router-dom
  loc: 156
- file: frontend-vite/src/components/common/Sidebar.jsx
  element: Sidebar
  type: Component
  description: 'Sidebar navigation component with links to main application sections
    and active state highlighting.

    '
  confidence: 70
  dependencies:
  - react-router-dom
  loc: 120
- file: frontend-vite/src/contexts/CodingAgentContext.jsx
  element: CodingAgentContext
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  loc: 25
- file: frontend-vite/src/contexts/NotificationContext.jsx
  element: NotificationContext
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  loc: 65
- file: frontend-vite/src/contexts/ProjectContext.jsx
  element: ProjectContext
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  loc: 158
- file: frontend-vite/src/contexts/ProjectContext.jsx
  element: ProjectProvider
  type: Context
  description: 'Context provider for managing project state, including current project,
    recent projects, and project operations.

    '
  dependencies:
  - react
  - api
  loc: 120
- file: frontend-vite/src/hooks/useProject.js
  element: useProject
  type: Other
  description: 'Custom hook for accessing the ProjectContext, providing a convenient
    way to use project state and functions.

    '
  confidence: 70
  dependencies:
  - react
  loc: 10
- file: frontend-vite/src/main.jsx
  element: main
  type: Other
  description: 'Entry point for the Vite React application that renders the App component
    and logs environment variables.

    '
  confidence: 70
  dependencies:
  - react
  loc: 14
- file: frontend-vite/src/services/api.js
  element: api
  type: Other
  description: 'API service for making requests to the backend, with endpoint functions
    for projects, tasks, graphs, and LLM.

    '
  confidence: 70
  dependencies:
  - axios
  loc: 130
- file: frontend-vite/src/utils/apiUtils.js
  element: apiUtils
  type: Utility
  description: 'TODO: Add description'
  confidence: 0
  dependencies: []
  loc: 46
- file: frontend/src/App.js
  element: App
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  - react-router-dom
  loc: 35
- file: frontend/src/components/common/ApiStatus.js
  element: ApiStatus
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - axios
  - react
  loc: 82
- file: frontend/src/components/common/Navbar.js
  element: Navbar
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  - react-router-dom
  loc: 36
- file: frontend/src/components/common/Sidebar.js
  element: Sidebar
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  - react-router-dom
  loc: 58
- file: frontend/src/components/debug/ApiDebugger.js
  element: ApiDebugger
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - axios
  - react
  loc: 158
- file: frontend/src/index.js
  element: index
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  loc: 12
- file: frontend/src/pages/Dashboard.js
  element: Dashboard
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  - react-router-dom
  loc: 103
- file: frontend/src/pages/Debug.js
  element: Debug
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  loc: 62
- file: frontend/src/pages/ProjectList.js
  element: ProjectList
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  - react-router-dom
  loc: 157
- file: frontend/src/reportWebVitals.js
  element: reportWebVitals
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies: []
  loc: 12
- file: frontend/src/services/api.js
  element: api
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - axios
  loc: 44
- file: src/App.jsx
  element: App
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  loc: 18
- file: src/main.jsx
  element: main
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - react
  loc: 9
- file: test-frontend/src/App.js
  element: App
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies: []
  loc: 0
- file: test-frontend/src/index.js
  element: index
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies: []
  loc: 0
- file: test_backend.py
  element: test_backend
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  dependencies:
  - traceback
  loc: 21
- file: test_mvcd_service.py
  element: test_mvcd_service
  type: Utility
  description: 'TODO: Add description'
  confidence: 0
  dependencies: []
  loc: 27
