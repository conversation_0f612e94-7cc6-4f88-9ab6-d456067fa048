codebase:
- file: augment_mvcd_helper.py
  element: load_prompt
  type: Other
  description: "Loads the prompt text from a specified file path for use in the MVCD enrichment process."
  confidence: 95
  status: active
  dependencies:
  - argparse
  - pyperclip
  - subprocess
  - threading
  - traceback
  - yaml
  loc: 271
  last_modified: 1747746055.7223716
- file: backend/app/__init__.py
  element: __init__
  type: Other
  description: "TODO: Add description"
  confidence: 0
  status: active
  dependencies: []
  loc: 1
  last_modified: 1746734498.8320613
- file: backend/app/api/__init__.py
  element: __init__
  type: Other
  description: "TODO: Add description"
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1746701190.768119
- file: backend/app/api/endpoints/__init__.py
  element: __init__
  type: Other
  description: "Initializes the API endpoints module for the Vibe Architect application."
  confidence: 90
  status: active
  dependencies: []
  loc: 1
  last_modified: 1747655963.112457
- file: backend/app/api/endpoints/architecture.py
  element: get_file_hash
  type: Other
  description: "Calculates and returns the MD5 hash of a file to detect changes."
  confidence: 95
  status: active
  dependencies:
  - fastapi
  - hashlib
  loc: 68
  last_modified: **********.753634
- file: backend/app/api/endpoints/graph.py
  element: get_graph_data
  type: Other
  description: "Retrieves graph data for a project to support visualization features."
  confidence: 90
  status: active
  dependencies:
  - fastapi
  - tempfile
  loc: 35
  last_modified: **********.981479
- file: backend/app/api/endpoints/llm.py
  element: list_providers
  type: Other
  description: "Lists available Large Language Model (LLM) providers for the system."
  confidence: 90
  status: active
  dependencies:
  - fastapi
  loc: 32
  last_modified: **********.9774485
- file: backend/app/api/endpoints/mvcd.py
  element: MVCDStatusResponse
  type: Other
  description: "Pydantic response model representing the status and metrics of the MVCD file."
  confidence: 95
  status: active
  dependencies:
  - fastapi
  - pydantic
  - subprocess
  - threading
  loc: 466
  last_modified: **********.655621
- file: backend/app/api/endpoints/projects.py
  element: list_projects
  type: Other
  description: "Lists all Vibe Architect projects in a specified base directory."
  confidence: 90
  status: active
  dependencies:
  - fastapi
  - pydantic
  loc: 80
  last_modified: **********.0239427
- file: backend/app/api/endpoints/tasks.py
  element: list_tasks
  type: Other
  description: "Lists all tasks for a project, optionally filtered by status."
  confidence: 90
  status: active
  dependencies:
  - fastapi
  loc: 86
  last_modified: **********.4587839
- file: backend/app/api/router.py
  element: health_check
  type: Other
  description: "Health check endpoint for the API, returns status and message."
  confidence: 95
  status: active
  dependencies:
  - fastapi
  loc: 15
  last_modified: **********.5552886
- file: backend/app/core/__init__.py
  element: __init__
  type: Other
  description: "TODO: Add description"
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: **********.5225384
- file: backend/app/core/config.py
  element: Settings
  type: Other
  description: "Configuration class for API, CORS, LLM, and file path settings using Pydantic BaseSettings."
  confidence: 95
  status: active
  dependencies:
  - pydantic
  - pydantic_settings
  - yaml
  loc: 41
  last_modified: **********.8720632
- file: backend/app/core/project_manager.py
  element: ProjectManager
  type: Other
  description: "Manages project directories, files, and settings for Vibe Architect projects."
  confidence: 95
  status: active
  dependencies:
  - shutil
  loc: 97
  last_modified: **********.2727323
- file: backend/app/main.py
  element: health_check
  type: Other
  description: "Health check endpoint for the API with explicit CORS headers."
  confidence: 95
  status: active
  dependencies:
  - fastapi
  - uvicorn
  loc: 41
  last_modified: **********.747401
- file: backend/app/models/__init__.py
  element: __init__
  type: Other
  description: "TODO: Add description"
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: **********.2250445
- file: backend/app/models/project.py
  element: Project
  type: Other
  description: "Pydantic model representing a Vibe Architect project, including name, path, settings, and tasks."
  confidence: 95
  status: active
  dependencies:
  - pydantic
  loc: 9
  last_modified: **********.5967286
- file: backend/app/models/task.py
  element: TaskStatus
  type: Other
  description: "Enumeration of possible task statuses: TODO and DONE."
  confidence: 95
  status: active
  dependencies:
  - pydantic
  loc: 12
  last_modified: **********.268391
- file: backend/app/routers/directory.py
  element: list_directories
  type: Other
  description: "Lists all directories in a given path using DirectoryService."
  confidence: 90
  status: active
  dependencies:
  - fastapi
  - logging
  loc: 29
  last_modified: **********.3949704
- file: backend/app/schemas/__init__.py
  element: __init__
  type: Other
  description: "TODO: Add description"
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: **********.129441
- file: backend/app/schemas/llm.py
  element: LLMProvider
  type: Other
  description: "Pydantic schema for representing an LLM provider, including id, name, and models."
  confidence: 95
  status: active
  dependencies:
  - pydantic
  loc: 13
  last_modified: **********.701626
- file: backend/app/schemas/project.py
  element: ProjectBase
  type: Other
  description: "Base schema for project operations, including optional project name."
  confidence: 95
  status: active
  dependencies:
  - pydantic
  loc: 21
  last_modified: **********.2858427
- file: backend/app/schemas/project_check.py
  element: ProjectCheckRequest
  type: Other
  description: "Request schema for checking if a directory is a Vibe Architect project."
  confidence: 95
  status: active
  dependencies:
  - pydantic
  loc: 9
  last_modified: **********.5844054
- file: backend/app/schemas/task.py
  element: TaskBase
  type: Other
  description: "Base schema for task operations, including title, description, status, and dependencies."
  confidence: 95
  status: active
  dependencies:
  - pydantic
  loc: 19
  last_modified: **********.3351848
- file: backend/app/services/__init__.py
  element: __init__
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1746701187.0793188
- file: backend/app/services/directory_service.py
  element: DirectoryService
  type: Utility
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - logging
  loc: 41
  last_modified: **********.392154
- file: backend/app/services/file_service.py
  element: FileService
  type: Utility
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - yaml
  loc: 82
  last_modified: 1746701044.5235724
- file: backend/app/services/graph_service.py
  element: GraphService
  type: Utility
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - matplotlib
  - networkx
  loc: 74
  last_modified: 1746701131.6453714
- file: backend/app/services/llm_service.py
  element: LLMService
  type: Utility
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 43
  last_modified: 1746701145.088758
- file: backend/app/services/mvcd_enrichment.py
  element: MVCDEnrichmentService
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - argparse
  - logging
  - pyperclip
  - webbrowser
  - yaml
  loc: 223
  last_modified: **********.3739245
- file: backend/app/services/mvcd_service.py
  element: MVCDService
  type: Context
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - fnmatch
  - yaml
  loc: 239
  last_modified: 1747748874.1204908
- file: backend/middleware/static_files.py
  element: VibeArchStaticFiles
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - fastapi
  loc: 39
  last_modified: 1747685009.234377
- file: backend/setup.py
  element: setup
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - setuptools
  loc: 19
  last_modified: 1746734511.1099174
- file: enrich_mvcd.py
  element: enrich_mvcd
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 6
  last_modified: 1747609547.7567697
- file: frontend-vite/src/App.jsx
  element: App
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-router-dom
  loc: 105
  last_modified: **********.41502
- file: frontend-vite/src/EnvTest.jsx
  element: EnvTest
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 25
  last_modified: 1747748407.204539
- file: frontend-vite/src/components/ApiTest.jsx
  element: ApiTest
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - axios
  - react
  loc: 121
  last_modified: 1747588573.554899
- file: frontend-vite/src/components/AppLayout.jsx
  element: AppLayout
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 13
  last_modified: 1747652048.0654404
- file: frontend-vite/src/components/Architecture.jsx
  element: Architecture
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - mermaid
  - react
  loc: 292
  last_modified: 1747586939.6878846
- file: frontend-vite/src/components/ClearStorage.jsx
  element: ClearStorage
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 55
  last_modified: 1747685448.4288437
- file: frontend-vite/src/components/Dashboard.jsx
  element: Dashboard
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-router-dom
  loc: 285
  last_modified: 1747689842.4372528
- file: frontend-vite/src/components/Directory.jsx
  element: Directory
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 142
  last_modified: 1747687333.9284453
- file: frontend-vite/src/components/FileBrowser.jsx
  element: FileBrowser
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 158
  last_modified: 1747686985.7885532
- file: frontend-vite/src/components/Layout.jsx
  element: Layout
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 59
  last_modified: 1747746664.66801
- file: frontend-vite/src/components/MVCD.jsx
  element: MVCD
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - js-yaml
  - react
  loc: 785
  last_modified: 1747750965.219604
- file: frontend-vite/src/components/Notification.jsx
  element: Notification
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 102
  last_modified: 1747690157.3626318
- file: frontend-vite/src/components/ProjectSelection.jsx
  element: ProjectSelection
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 318
  last_modified: 1747685704.1265574
- file: frontend-vite/src/components/Projects.jsx
  element: Projects
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 302
  last_modified: 1747690264.4857745
- file: frontend-vite/src/components/common/AppHeader.jsx
  element: AppHeader
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-router-dom
  loc: 18
  last_modified: 1747655021.9440346
- file: frontend-vite/src/components/common/HeaderTest.jsx
  element: HeaderTest
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 9
  last_modified: 1747649145.7030718
- file: frontend-vite/src/components/common/Navbar.jsx
  element: Navbar
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-router-dom
  loc: 156
  last_modified: 1747655072.0684154
- file: frontend-vite/src/components/common/Sidebar.jsx
  element: Sidebar
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react-router-dom
  loc: 120
  last_modified: 1747656135.4305513
- file: frontend-vite/src/contexts/CodingAgentContext.jsx
  element: CodingAgentContext
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 25
  last_modified: 1747689669.7245343
- file: frontend-vite/src/contexts/NotificationContext.jsx
  element: NotificationContext
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 65
  last_modified: 1747690176.6664555
- file: frontend-vite/src/contexts/ProjectContext.jsx
  element: ProjectContext
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 158
  last_modified: 1747690025.9762847
- file: frontend-vite/src/hooks/useProject.js
  element: useProject
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 10
  last_modified: 1747586781.441354
- file: frontend-vite/src/main.jsx
  element: main
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 10
  last_modified: 1747748390.7104452
- file: frontend-vite/src/services/api.js
  element: api
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - axios
  loc: 130
  last_modified: 1747748426.8382118
- file: frontend-vite/src/utils/apiUtils.js
  element: apiUtils
  type: Utility
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 46
  last_modified: 1747689956.3461094
- file: src/App.jsx
  element: App
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 18
  last_modified: 1747684341.051617
- file: src/main.jsx
  element: main
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 9
  last_modified: 1747684333.2712677
- file: test-frontend/src/App.js
  element: App
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1746994095.8376603
- file: test-frontend/src/index.js
  element: index
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1746994095.8639896
- file: test_backend.py
  element: test_backend
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - traceback
  loc: 21
  last_modified: 1747685052.8380988
- file: test_mvcd_enrichment.py
  element: create_log_file
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - subprocess
  - threading
  loc: 140
  last_modified: 1747766450.9786942
- file: test_mvcd_service.py
  element: test_mvcd_service
  type: Utility
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 27
  last_modified: 1747608814.5829353
