# API Documentation

## Base URL

```
http://localhost:8080
```

## Authentication

[Authentication details]

## Endpoints

### Projects

#### Get All Projects

**URL:** `/api/projects`

**Method:** `GET`

**Query Parameters:**
- `base_directory` (string, required): The base directory to scan for projects

**Response:**
```json
[
  {
    "id": "string",
    "name": "string",
    "path": "string",
    "description": "string",
    "created": "string (ISO date)",
    "lastModified": "string (ISO date)",
    "tasks": {
      "total": 0,
      "completed": 0
    }
  }
]
```

#### Get Project by ID

**URL:** `/api/projects/{project_id}`

**Method:** `GET`

**URL Parameters:**
- `project_id` (string, required): The ID of the project

**Response:**
```json
{
  "id": "string",
  "name": "string",
  "path": "string",
  "description": "string",
  "created": "string (ISO date)",
  "lastModified": "string (ISO date)",
  "tasks": {
    "total": 0,
    "completed": 0
  },
  "specification": "string",
  "architecture": {},
  "techStack": {},
  "documentation": {}
}
```

#### Create Project

**URL:** `/api/projects`

**Method:** `POST`

**Request:**
```json
{
  "name": "string",
  "path": "string",
  "description": "string"
}
```

**Response:**
```json
{
  "id": "string",
  "name": "string",
  "path": "string",
  "description": "string",
  "created": "string (ISO date)",
  "lastModified": "string (ISO date)"
}
```

### Tasks

#### Get All Tasks

**URL:** `/api/projects/{project_id}/tasks`

**Method:** `GET`

**URL Parameters:**
- `project_id` (string, required): The ID of the project

**Response:**
```json
[
  {
    "id": "string",
    "title": "string",
    "description": "string",
    "status": "string",
    "created": "string (ISO date)",
    "lastModified": "string (ISO date)"
  }
]
```

#### Get Task by ID

**URL:** `/api/projects/{project_id}/tasks/{task_id}`

**Method:** `GET`

**URL Parameters:**
- `project_id` (string, required): The ID of the project
- `task_id` (string, required): The ID of the task

**Response:**
```json
{
  "id": "string",
  "title": "string",
  "description": "string",
  "status": "string",
  "created": "string (ISO date)",
  "lastModified": "string (ISO date)"
}
```

#### Create Task

**URL:** `/api/projects/{project_id}/tasks`

**Method:** `POST`

**URL Parameters:**
- `project_id` (string, required): The ID of the project

**Request:**
```json
{
  "title": "string",
  "description": "string",
  "status": "string"
}
```

**Response:**
```json
{
  "id": "string",
  "title": "string",
  "description": "string",
  "status": "string",
  "created": "string (ISO date)",
  "lastModified": "string (ISO date)"
}
```

### LLM

#### Generate Text

**URL:** `/api/llm/generate`

**Method:** `POST`

**Request:**
```json
{
  "prompt": "string",
  "model": "string",
  "max_tokens": 0,
  "temperature": 0
}
```

**Response:**
```json
{
  "text": "string",
  "model": "string",
  "tokens": 0
}
```

#### Get Available Models

**URL:** `/api/llm/models`

**Method:** `GET`

**Response:**
```json
[
  {
    "id": "string",
    "name": "string",
    "provider": "string",
    "maxTokens": 0
  }
]
```

## Error Responses

**400 Bad Request:**
```json
{
  "error": "string",
  "message": "string"
}
```

**404 Not Found:**
```json
{
  "error": "string",
  "message": "string"
}
```

**500 Internal Server Error:**
```json
{
  "error": "string",
  "message": "string"
}
```
