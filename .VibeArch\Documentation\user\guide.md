# User Guide

## Introduction

Welcome to the Vibe Architect user guide. This document will help you get started with the application and provide detailed instructions on how to use its features.

## Getting Started

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/vibe-architect.git
   cd vibe-architect
   ```

2. Create a `.env` file from the example:
   ```bash
   cp .env.example .env
   ```

3. Edit the `.env` file to add your API keys.

### Running the Application

Run both backend and frontend with a single command:
```powershell
# On Windows with Vite + React (recommended)
.\run.ps1
```

This script will:
- Check for required dependencies (Python, Node.js, npm)
- Install all necessary packages
- Start both the backend and frontend servers in separate windows
- Open the application in your default browser

## Features

### Project Management

#### Selecting a Project Directory

1. From the Dashboard or Projects page, click on the "Select Directory" button.
2. Enter the path to your project directory or use the "Browse" button to select it.
3. Click "Scan Directory" to scan for projects.

#### Creating a New Project

1. From the Projects page, click on the "New Project" button.
2. Enter the project name and path.
3. Click "Create" to create the project.

#### Opening a Project

1. From the Projects page, find the project you want to open.
2. Click the "Open" button next to the project.

### Task Management

#### Creating a Task

1. From the Tasks page, click on the "New Task" button.
2. Enter the task title, description, and status.
3. Click "Create" to create the task.

#### Updating a Task

1. From the Tasks page, find the task you want to update.
2. Click on the task to open it.
3. Update the task details.
4. Click "Save" to save the changes.

#### Completing a Task

1. From the Tasks page, find the task you want to complete.
2. Click the checkbox next to the task to mark it as complete.

### LLM Integration

#### Configuring LLM Providers

1. From the Settings page, go to the "LLM" tab.
2. Enter your API keys for the LLM providers you want to use.
3. Click "Save" to save the changes.

#### Using the LLM Interface

1. From the LLM page, select the model you want to use.
2. Enter your prompt in the text area.
3. Click "Generate" to generate text.

### Visualization

#### Viewing Project Structure

1. From the Visualization page, select "Project Structure" from the dropdown.
2. The project structure will be displayed as a graph.

#### Viewing Task Dependencies

1. From the Visualization page, select "Task Dependencies" from the dropdown.
2. The task dependencies will be displayed as a graph.

#### Exporting Graphs

1. From the Visualization page, click the "Export" button.
2. Select the format you want to export to (PNG or SVG).
3. Click "Export" to export the graph.

## Troubleshooting

### Common Issues

#### Port Conflicts

If you encounter port conflicts, you can use the `kill-ports.ps1` script to free up the required ports:

```powershell
.\kill-ports.ps1
```

#### Backend Issues

- **Module Not Found Error**: Make sure to set the PYTHONPATH environment variable to include the project root directory.
- **Import Errors**: Ensure you've installed the backend package in development mode with `pip install -e .` from the backend directory.

#### Frontend Issues

- **npm Not Found**: Make sure Node.js is installed and npm is in your PATH. On Windows, you may need to add `C:\Program Files\nodejs` to your PATH.
- **Vite Issues**: Make sure you're using Node.js 20+ and have installed all dependencies with `npm install` in the `frontend-vite` directory.

## Support

If you encounter any issues or have questions, please open an issue on the GitHub repository or contact the development team.
