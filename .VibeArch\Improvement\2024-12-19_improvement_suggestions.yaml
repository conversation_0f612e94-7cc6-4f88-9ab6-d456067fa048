- issue_id: IMP001
  file: frontend-vite/src/components/MVCD.jsx
  element: fetchMvcdData
  line: 319
  category: structure
  issue: "Function is too long (35+ lines) and handles multiple responsibilities including error handling, data parsing, and state management."
  suggestion: "Break down into smaller functions: parseYamlData(), buildDirectoryStructure(), and handleMvcdError()."
  confidence: 85
  impact_level: medium

- issue_id: IMP002
  file: backend/app/api/endpoints/mvcd.py
  element: get_improvements
  line: 340
  category: performance
  issue: "File parsing happens on every request without caching mechanism."
  suggestion: "Implement caching mechanism for parsed improvement files to reduce I/O operations and improve response times."
  confidence: 90
  impact_level: high

- issue_id: IMP003
  file: frontend-vite/src/services/api.js
  element: mvcdApi
  line: 131
  category: clarity
  issue: "API endpoint functions lack proper error handling documentation and JSDoc comments."
  suggestion: "Add comprehensive JSDoc comments documenting expected error responses, status codes, and parameter types."
  confidence: 75
  impact_level: low

- issue_id: IMP004
  file: backend/app/api/endpoints/mvcd.py
  element: _parse_markdown_improvements
  line: 75
  category: redundancy
  issue: "Regex pattern matching logic is duplicated and could be more robust for edge cases."
  suggestion: "Create a centralized pattern matching utility with comprehensive regex patterns and validation."
  confidence: 80
  impact_level: medium

- issue_id: IMP005
  file: frontend-vite/src/components/MVCD.jsx
  element: handlePriorityChange
  line: 506
  category: semantics
  issue: "Timestamp generation logic is scattered across multiple functions without consistent formatting."
  suggestion: "Create a utility function for timestamp generation and formatting to ensure consistency across the application."
  confidence: 70
  impact_level: low

- issue_id: IMP006
  file: backend/app/api/endpoints/mvcd.py
  element: running_tasks
  line: 73
  category: security
  issue: "Global task storage dictionary could lead to memory leaks and potential security issues with long-running processes."
  suggestion: "Implement proper task cleanup mechanism and consider using a more secure task queue system like Celery or Redis."
  confidence: 85
  impact_level: high

- issue_id: IMP007
  file: frontend-vite/src/components/MVCD.jsx
  element: getActiveProject
  line: 166
  category: unused
  issue: "Function has multiple fallback mechanisms that may never be used in practice."
  suggestion: "Analyze usage patterns and remove unnecessary fallback logic or add proper error handling."
  confidence: 65
  impact_level: low
