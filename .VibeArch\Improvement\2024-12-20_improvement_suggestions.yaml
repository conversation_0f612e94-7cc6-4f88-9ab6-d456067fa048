- issue_id: IMP001
  file: backend/app/api/endpoints/mvcd.py
  element: _parse_markdown_improvements
  line: 75
  category: redundancy
  issue: "Function contains duplicate regex matching logic that is repeated in multiple places within the same function."
  suggestion: "Extract regex pattern matching into a separate utility function to eliminate code duplication and improve maintainability."
  confidence: 85
  impact_level: medium

- issue_id: IMP002
  file: backend/app/api/endpoints/mvcd.py
  element: running_tasks
  line: 73
  category: security
  issue: "Global dictionary for storing task state could lead to memory leaks and potential security issues with long-running processes."
  suggestion: "Implement proper task cleanup mechanism and consider using a more secure task queue system like Celery or Redis with TTL."
  confidence: 90
  impact_level: high

- issue_id: IMP003
  file: frontend-vite/src/components/MVCD.jsx
  element: fetchMvcdData
  line: 319
  category: structure
  issue: "Function is too long (35+ lines) and handles multiple responsibilities including error handling, data parsing, and state management."
  suggestion: "Break down into smaller functions: parseYamlData(), buildDirectoryStructure(), and handleMvcdError()."
  confidence: 85
  impact_level: medium

- issue_id: IMP004
  file: frontend-vite/src/components/MVCD.jsx
  element: getActiveProject
  line: 166
  category: redundancy
  issue: "Function logic is duplicated across multiple components and contains repetitive localStorage access patterns."
  suggestion: "Move this logic to a custom hook (useActiveProject) or context to eliminate duplication across components."
  confidence: 80
  impact_level: medium

- issue_id: IMP005
  file: backend/app/services/mvcd_service.py
  element: detect_dependencies
  line: 172
  category: performance
  issue: "Function reads entire file content into memory and performs multiple regex operations on large files."
  suggestion: "Implement streaming file reading and compile regex patterns once to improve performance for large codebases."
  confidence: 75
  impact_level: medium

- issue_id: IMP006
  file: backend/app/services/mvcd_service.py
  element: should_ignore
  line: 87
  category: performance
  issue: "Function performs pattern matching on every file/directory without caching compiled patterns."
  suggestion: "Pre-compile fnmatch patterns and cache results to improve performance during codebase scanning."
  confidence: 80
  impact_level: medium

- issue_id: IMP007
  file: augment_mvcd_helper.py
  element: is_autohotkey_installed
  line: 138
  category: semantics
  issue: "Function bypasses actual AutoHotkey detection and always returns True, making error handling unreliable."
  suggestion: "Implement proper AutoHotkey detection or remove the function entirely if detection is not needed."
  confidence: 90
  impact_level: low

- issue_id: IMP008
  file: frontend-vite/src/components/MVCD.jsx
  element: MVCD
  line: 7
  category: structure
  issue: "Component has grown to 1392 lines and manages too many responsibilities (status, data fetching, UI rendering, task management)."
  suggestion: "Split into smaller components: MVCDStatus, MVCDWorkflow, MVCDDataView, and MVCDTabs to improve maintainability."
  confidence: 95
  impact_level: high

- issue_id: IMP009
  file: backend/app/api/endpoints/mvcd.py
  element: calculate_mvcd_metrics
  line: 202
  category: redundancy
  issue: "Function contains repetitive confidence calculation logic for frontend and backend entries."
  suggestion: "Extract confidence calculation into a helper function that accepts entry lists and returns metrics."
  confidence: 75
  impact_level: low

- issue_id: IMP010
  file: backend/app/services/mvcd_service.py
  element: detect_element_type
  line: 222
  category: clarity
  issue: "Function uses hardcoded string matching and regex patterns that are difficult to maintain and extend."
  suggestion: "Create a configuration-driven approach with extensible rules for element type detection."
  confidence: 70
  impact_level: medium

- issue_id: IMP011
  file: frontend-vite/src/services/api.js
  element: mvcdApi
  line: 1169
  category: clarity
  issue: "API functions lack proper JSDoc documentation and error handling patterns are inconsistent."
  suggestion: "Add comprehensive JSDoc comments and standardize error handling across all API functions."
  confidence: 80
  impact_level: low

- issue_id: IMP012
  file: backend/app/api/endpoints/mvcd.py
  element: get_improvements
  line: 357
  category: performance
  issue: "Function parses all improvement files on every request without any caching mechanism."
  suggestion: "Implement file-based caching with modification time checks to avoid re-parsing unchanged files."
  confidence: 85
  impact_level: medium

- issue_id: IMP013
  file: augment_mvcd_helper.py
  element: run_autohotkey_script
  line: 240
  category: structure
  issue: "Function contains hardcoded paths and complex fallback logic that makes it difficult to maintain."
  suggestion: "Create a configuration file for AutoHotkey paths and simplify the detection logic."
  confidence: 75
  impact_level: medium

- issue_id: IMP014
  file: backend/app/services/mvcd_service.py
  element: merge_with_existing
  line: 375
  category: semantics
  issue: "Function has complex nested logic for preserving existing data that could lead to data inconsistencies."
  suggestion: "Simplify the merge logic and add validation to ensure data integrity during the merge process."
  confidence: 80
  impact_level: medium

- issue_id: IMP015
  file: frontend-vite/src/components/MVCD.jsx
  element: useEffect
  line: 65
  category: performance
  issue: "Multiple useEffect hooks with overlapping dependencies could cause unnecessary re-renders and API calls."
  suggestion: "Consolidate related useEffect hooks and optimize dependency arrays to reduce unnecessary executions."
  confidence: 75
  impact_level: low
