# AutoHotKey Automation for Coding Agent Interaction

## Overview

This document outlines the approach for automating interactions with coding agents (like Augment, Cursor) using AutoHotKey, a powerful automation tool for Windows. This approach replaces the previous Playwright-based solution with a more reliable and simpler implementation.

## Why AutoHotKey?

AutoHotKey was chosen for several key reasons:

1. **Universal Interface**: Uses keyboard and clipboard operations that work across any application
2. **Minimal Dependencies**: Lightweight tool with no complex browser automation requirements
3. **Resilience to Change**: Not dependent on UI elements or DOM structures that might change
4. **Simplicity**: Easy to understand, modify, and maintain
5. **Reliability**: Less prone to breaking due to UI changes in VSCode or coding agents

## Implementation Details

### Core Components

1. **AutoHotKey Script** (`augment_mvcd_automation.ahk`):
   - Reads the prompt from a file
   - Copies it to the clipboard
   - Activates VSCode window
   - Opens the coding agent chat
   - Pastes the prompt and sends it

2. **Python Helper** (`augment_mvcd_helper.py`):
   - Provides a user-friendly interface for manual interaction
   - Handles clipboard operations for both input and output
   - Validates and saves the response

3. **Backend Integration**:
   - Triggers the AutoHotKey script via subprocess
   - Monitors the MVCD file for changes
   - Updates the UI with progress information

### AutoHotKey Script

```ahk
; augment_mvcd_automation.ahk

; Read the prompt from file
FileRead, PromptText, .VibeArch\VibeArch_Setup\mvcd_description_enrichment_prompt.yaml
if ErrorLevel {
    FileAppend, Error: Could not read prompt file`n, automation_log.txt
    ExitApp, 1
}

; Copy to clipboard
Clipboard := PromptText

; Activate VSCode window
WinActivate, ahk_exe Code.exe
if ErrorLevel {
    FileAppend, Error: Could not activate VSCode`n, automation_log.txt
    ExitApp, 1
}

; Open Augment chat
Send, ^+p
Sleep, 300
Send, Augment: Open Chat
Send, {Enter}
Sleep, 1000

; Paste and send prompt
Send, ^v
Sleep, 100
Send, {Enter}

; Log success and exit
FileAppend, Success: Prompt inserted into Augment chat`n, automation_log.txt
ExitApp, 0
```

### Python Helper Script

```python
# augment_mvcd_helper.py

import os
import sys
import argparse
import pyperclip
from pathlib import Path

def load_prompt(prompt_path):
    """Load the prompt from the specified file."""
    try:
        with open(prompt_path, "r", encoding="utf-8") as f:
            prompt = f.read()
        print(f"Loaded prompt ({len(prompt)} characters) from {prompt_path}")
        return prompt
    except Exception as e:
        print(f"Error loading prompt: {e}")
        return None

def save_response(response_path):
    """Save the response to the specified file."""
    try:
        response = pyperclip.paste()

        # Check if the response starts with "codebase:"
        if not response.strip().startswith("codebase:"):
            print("Error: The clipboard content doesn't appear to be a valid MVCD YAML response.")
            print("Make sure you've copied the entire response from Augment, starting with 'codebase:'")
            return False

        # Save the response
        with open(response_path, "w", encoding="utf-8") as f:
            f.write(response)

        print(f"Saved response to {response_path}")
        return True
    except Exception as e:
        print(f"Error saving response: {e}")
        return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Augment MVCD Helper")
    parser.add_argument("--prompt", help="Path to the prompt file",
                        default=".VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml")
    parser.add_argument("--output", help="Path to save the response",
                        default=".VibeArch/Directory/mvcd.yaml")
    args = parser.parse_args()

    # Convert paths to absolute paths
    prompt_path = Path(args.prompt).resolve()
    output_path = Path(args.output).resolve()

    # Load the prompt
    prompt = load_prompt(prompt_path)
    if not prompt:
        sys.exit(1)

    # Copy the prompt to the clipboard
    pyperclip.copy(prompt)
    print("Prompt copied to clipboard!")

    # Provide instructions to the user
    print("\n" + "="*80)
    print("INSTRUCTIONS:")
    print("1. Open the Augment chat in VSCode")
    print("2. Paste the prompt (Ctrl+V) into the chat input")
    print("3. Press Enter to send the prompt")
    print("4. Wait for Augment to process and respond")
    print("5. Once Augment has finished responding, select and copy (Ctrl+A, Ctrl+C) the entire response")
    print("6. Return to this terminal window and press Enter to continue")
    print("="*80 + "\n")

    # Wait for user to press Enter
    input("Press Enter when you've copied the response from Augment...")

    # Save the response
    if save_response(output_path):
        print("\nSuccess! The MVCD file has been updated.")
        sys.exit(0)
    else:
        print("\nFailed to save the response. Please try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

## Backend Integration

The backend integrates with this automation approach by:

1. **Triggering the AutoHotKey Script**:
   ```python
   def run_mvcd_enrichment(project_path, config):
       if config["coding_agent_type"].lower() == "augment":
           try:
               # Run the AutoHotKey script
               ahk_script_path = os.path.join(project_path, "scripts", "augment_mvcd_automation.ahk")
               subprocess.run(["AutoHotkey.exe", ahk_script_path], check=True)

               # Update task status
               running_tasks[task_id] = {
                   "status": "running",
                   "message": "Prompt inserted into Augment chat. Monitoring for changes..."
               }

               # Start a file watcher to monitor the MVCD file for changes
               start_mvcd_file_watcher(project_path, task_id)

           except subprocess.CalledProcessError:
               running_tasks[task_id] = {
                   "status": "failed",
                   "message": "Failed to insert prompt into Augment chat"
               }
   ```

2. **Monitoring for Changes**:
   ```python
   def start_mvcd_file_watcher(project_path, task_id):
       """Start a background thread to watch for changes to the MVCD file."""
       mvcd_path = os.path.join(project_path, ".VibeArch", "Directory", "mvcd.yaml")

       def watch_file():
           last_modified = 0
           check_interval = 2  # seconds

           while True:
               try:
                   current_modified = os.path.getmtime(mvcd_path)

                   if current_modified > last_modified:
                       last_modified = current_modified

                       # Calculate progress metrics
                       metrics = calculate_mvcd_metrics(project_path)

                       # Update task status with progress information
                       running_tasks[task_id] = {
                           "status": "running",
                           "message": f"Processing... {metrics['entries_with_descriptions']}/{metrics['total_entries']} entries enriched.",
                           "progress": metrics
                       }

                       # Check if all entries have been processed
                       if metrics['entries_with_descriptions'] == metrics['total_entries']:
                           running_tasks[task_id] = {
                               "status": "completed",
                               "message": "MVCD enrichment completed successfully"
                           }
                           return

                   time.sleep(check_interval)

               except Exception as e:
                   running_tasks[task_id] = {
                       "status": "failed",
                       "message": f"Error monitoring MVCD file: {str(e)}"
                   }
                   return

       # Start the watcher in a background thread
       thread = threading.Thread(target=watch_file)
       thread.daemon = True
       thread.start()
   ```

## Advantages Over Playwright

1. **Simplicity**: The AutoHotKey approach is much simpler and more focused
2. **Reliability**: Less prone to breaking due to UI changes
3. **Performance**: Lighter weight and faster execution
4. **Maintainability**: Easier to understand and modify
5. **Flexibility**: Works with any coding agent that can be accessed via keyboard shortcuts

## Limitations

1. **Windows Only**: AutoHotKey is Windows-specific (alternatives exist for macOS/Linux)
2. **VSCode Focus**: Temporarily takes focus from other applications
3. **Manual Intervention**: May require manual intervention if unexpected dialogs appear

## Status Field for Components

The MVCD enrichment process now includes determining the status of each component:

1. **Status Values**:
   - `active`: Component is currently in use and maintained
   - `deprecated`: Component is being phased out or replaced

2. **Status Determination**:
   - The coding agent analyzes code patterns to identify deprecated components
   - Looks for comments like `// DEPRECATED` or `# TODO: Replace with...`
   - Checks for usage patterns that indicate deprecation
   - Examines import statements and dependency relationships

3. **Display in UI**:
   - Components are visually marked with status indicators (green/red badges)
   - Filtering options allow showing only active or deprecated components
   - Status history tracking shows when components were marked as deprecated

4. **Prompt Instructions**:
   - The enrichment prompt includes specific guidelines for determining status
   - Explains common patterns that indicate deprecation
   - Provides examples of how to identify deprecated components

## Future Improvements

1. **Cross-Platform Support**: Add equivalent scripts for macOS (AppleScript) and Linux (xdotool)
2. **Error Recovery**: Enhance error handling and recovery mechanisms
3. **Multiple Agent Support**: Add support for other coding agents (Cursor, ChatGPT)
4. **UI Integration**: Improve integration with the Vibe Architect UI
5. **Status Transitions**: Track when components change status from active to deprecated
6. **Deprecation Reasons**: Capture and display reasons for deprecation
