Objective

Design a system that allows the Vibe Architekt orchestrator to control and communicate with proprietary coding agents (e.g., Cursor, Augment) despite lacking native plugin or API access.

Core Challenge

Proprietary agents (Cursor, Augment Code):

Do not expose chatbox APIs

Do not support Agent2Agent (A2A) or MCP write-access

Cannot be prompted externally unless simulated or modified

Solution Pathways

1. Simulation via Browser Automation

Tools: Playwright, Selenium

Capabilities:

Simulate a user:

Launch app (Electron or web)

Insert text into chatboxes

Click buttons (e.g., "Submit")

Extract output (e.g., DOM scraping)

Pros:

Works even without plugin access

Non-invasive, tool-agnostic

Cons:

Fragile (depends on DOM selectors)

May break on updates

Needs GUI or headless environment

Use Case Fit:

Currently, this enables a more passive Vibe Architekt to operate via Playwright by acting as a simulated user. It can insert text (e.g., MCP links) into proprietary chatboxes and extract responses.

2. Fork and Extend Open Source Agent (e.g., Continue.dev)

Goal: Create a modifiable, VS Code-based coding agent with a controllable prompt interface.

Modification Points:

Add external API endpoint (e.g., /vibe/push_prompt)

Expose internal chat function

Route messages from orchestrator into the agent loop

Pros:

Full control

Can integrate architecture awareness, memory, task queues

Cons:

Doesn’t solve for Cursor/Augment directly

Requires user migration to the forked tool

3. Custom Orchestrator Agent Layer

Structure:

Backend: FastAPI

Memory Layer: YAML/VectorDB

Interface: Web UI or VS Code sidebar

Responsibilities:

Store architectural plan and agent goals

Dispatch instructions via file, API, or browser agent

Log and interpret agent outputs

Pros:

Full architectural control

System architect function is centralized

Cons:

Requires coding agents to participate (poll, watch file, or receive API push)

4. Manual MCP-Based Prompting

Concept: Use Model Context Protocol (MCP) links manually pasted into proprietary coding agent chatboxes.

How it works:

User pastes: [MCP: https://vibearchitekt.local/context/codeagent01]

The agent fetches the context file, e.g., YAML/JSON describing the next task.

Agent uses this to perform the next action.

Pros:

Compatible with ChatGPT desktop and future MCP-enabled agents

Requires no internal modifications or plugins

Keeps orchestrator in control of agent planning

Cons:

Manual interaction required

One-shot: agent has no memory unless context is reloaded each time

No orchestration trigger from the outside

5. A2A/MCP Future Compatibility Layer

Forward-looking Component:

Define A2A task cards

Mock endpoints for future A2A-compatible agents

Align with OpenAI/MCP specifications

Goal: Future-proof the orchestrator

6. AG-UI Protocol Integration

Concept: Use the AG-UI protocol as a lightweight, real-time, event-driven bridge between agents and the frontend interface.

How it works:

Implement AG-UI event streams (via SSE or WebSockets)

Stream task assignments, state changes, agent output, and UI updates in real time

Allow agents to dynamically interact with frontend elements (e.g., tool feedback, result visualization)

Pros:

Fully bidirectional and real-time

Agent can proactively update or react to UI

Standardized with growing adoption (LangGraph, CrewAI, etc.)

Cons:

Requires agents or proxies that support AG-UI

Needs integration layer with VS Code or frontend environment

Use Case Fit:

Ideal for bridging proprietary/closed agents with the orchestrator via external UI overlays or companion panels

Functional Requirements



Open Questions

Can we detect message/reply completion reliably in DOM scraping?

Which DOM selectors are stable in Cursor/Augment?

Do we simulate human typing or insert directly?

Should we run browser agent as part of orchestrator or as a separate process?

What AG-UI clients exist for VS Code or webview integration?

Can AG-UI events be simulated through the browser automation layer?

Next Steps

Prototype Playwright-based injection + readback on Cursor

Draft vibe/push_prompt endpoint for Continue.dev fork

Define YAML schema for agent task cards

Begin visual wireframe for orchestrator control panel

Explore AG-UI SDK and its integration with frontend overlays or LSP hooks

