ignore:
  - '**/__init__.py'
  - .git/
  - .venv/
  - node_modules/
  - __pycache__/
  - frontend/node_modules/
  - frontend-vite/node_modules/
  - test-frontend/node_modules/
  - frontend/assets/
  - frontend/styles/
  - frontend-vite/assets/
  - frontend-vite/styles/
  - '**/*.test.tsx'
  - '**/*.spec.ts'
  - '**/*.stories.tsx'
  - '**/__mocks__/**'
  - '**/*.md'
  - '**/*.svg'
  - '**/*.jpg'
  - '**/*.png'
  - '**/*.ico'
  - '**/*.css'
  - '**/*.scss'
  - '**/*.json'
  - '**/*.lock'
  - '**/*.config.js'
  - '**/*.config.ts'
  - '**/*.bat'
  - '**/*.ps1'
  - '**/*.egg-info/'
  - '.augmentignore'
  - '.augment_guidlines'
  - '.env'
  - '.env.example'
  - '.gitignore'
  - 'Public/'
