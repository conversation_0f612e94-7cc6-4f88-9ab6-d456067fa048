TASK: Build a complete functional summary of the codebase in YAML format,
#         optimized for reasoning, deduplication, and code analysis by LLMs.

# 📄 OUTPUT: Create a file named `mvcd.yaml` in .VibeArch/Directory.

# 📁 STRUCTURE: Use a flat list of entries. Each entry must follow the schema below.

codebase:
  - file: path/to/file.tsx                # Relative path from repo root
    element: ComponentOrFunctionName      # The exported function, hook, class, or constant
    type: Component | Hook | Utility | Store | Context | Type | Other
    description: >                        # 1–2 sentence human-readable explanation (what + why)
      Describes what this code does and why it exists in the system.
    confidence: 85                        # Confidence score (0-100) for the description accuracy
    status: active                        # Component status: active or deprecated
    dependencies: [react, axios]          # List of top-level imports (external only)
    loc: 42                               # Approximate number of lines of code (no blank/comments)
    last_modified: 1716144000             # Unix timestamp of when the file was last modified

# 🧼 IGNORE RULES (apply when crawling the codebase):
# These are specified in a separate file `.mvcd-ignore.yaml`

# Example `.mvcd-ignore.yaml` content:
ignore:
  - .git/
  - .venv/
  - node_modules/
  - __pycache__/
  - frontend/assets/
  - frontend/styles/
  - '**/*.test.tsx'
  - '**/*.spec.ts'
  - '**/*.stories.tsx'
  - '**/__mocks__/**'

# 🧠 Minimum Viable Code Description (MVCD) Specification

## Purpose
The MVCD schema provides a compact, machine-readable map of the codebase to support LLM-based code understanding, refactoring, and reasoning.

## Output File
- `mvcd.yaml` in root directory.

## Ignore Rules
Use `.mvcd-ignore.yaml` to exclude irrelevant files and folders. See committed ignore file.

## Entry Schema (per code unit)
```yaml
- file: path/to/File.tsx                # relative path from project root
  element: ExportedComponentOrFunction  # main export
  type: Component | Hook | Utility | Store | Context | Type | Other
  description: >
    One-line explanation of what the code does and why it exists.
  confidence: 85                        # confidence score (0-100) for description accuracy
  status: active                        # component status: active or deprecated
  dependencies: [react, axios]          # top-level external imports only
  loc: 42                               # lines of code excluding comments/blanks
  last_modified: 1716144000             # unix timestamp of when the file was last modified

# 📝 NOTES:
# - For files with multiple unrelated exports (e.g. a hook and a component), create separate entries.
# - Skip test files, styles, configs, and assets.
# - Use consistent formatting, and do not include full code snippets in the output.
# - If an entry does not clearly fall under a known type, label it `Other` and describe accordingly.

# ✅ GOAL:
# Create a lean, machine-readable map of the functional surface of the codebase,
# enabling LLMs and agents to identify redundancies, structure, and logic scope.

# 🛑 STRICT BOUNDARIES:
# - You are ONLY to scan and document existing code, not suggest improvements
# - You have NO architectural authority - do not make architectural judgments
# - Create entries ONLY for code that exists, not what you think should exist
# - Do NOT include any suggestions, recommendations, or critiques
# - Focus ONLY on factual documentation of what IS, not what SHOULD BE