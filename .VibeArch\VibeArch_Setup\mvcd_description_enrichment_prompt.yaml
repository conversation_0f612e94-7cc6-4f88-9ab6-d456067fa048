# Prompt: Enrich Descriptions and Add Confidence Scores in mvcd.yaml Using LLM

You are acting as a code analysis agent for the Vibe Architect system.

DISREGARD ALL OTHER CONTENT AND FOCUS ON THIS TASK

Your task is to enrich the "description" field for entries in an existing mvcd.yaml file and add confidence scores.
This file contains structural metadata about code components, but many of the entries currently include placeholder descriptions such as:

"TODO: Add description"

---

## Input

The YAML file is located at:

.VibeArch/Directory/mvcd.yaml

Each entry in the file includes the following fields:
- file: Relative path to the code file
- element: Name of the exported class, function, or component
- type: One of [Component, Hook, Utility, Store, Context, Type, Other]
- description: A short text (currently missing in many entries)
- status: Either "active" or "deprecated" (may be missing in many entries)
- dependencies: List of external libraries used
- loc: Approximate number of lines of code
- last_modified: Unix timestamp of when the file was last modified

---

## Goal

For each entry where the "description" is:
- "TODO: Add description"
- "MISSING"
- empty or null

You must:
1. Open and analyze the referenced code file
2. Determine what the specified element does and why it exists
3. Replace the placeholder with a short, meaningful, human-readable description
4. Add a confidence score indicating your certainty about the description's accuracy
5. Determine if the component is "active" or "deprecated" and add a status field

Descriptions should:
- Be 1–2 sentences
- Use neutral, professional technical language
- Clearly explain what the element does and its purpose in the system

Confidence scores should:
- Be a numeric value from 0-100%
- Reflect your certainty about the accuracy of your description
- Be based on:
  * Code clarity (well-named variables, functions, classes)
  * Presence of comments or docstrings
  * Consistency with other components you understand
  * Complexity of the code (simpler code = higher confidence)
- Follow these general guidelines:
  * 90-100%: Very clear purpose with good documentation
  * 70-89%: Clear purpose but limited documentation
  * 50-69%: Purpose can be inferred but with some uncertainty
  * 30-49%: Purpose is unclear or ambiguous
  * 0-29%: Unable to determine purpose with confidence

Status determination should:
- Classify each component as either "active" or "deprecated"
- Look for indicators of deprecation such as:
  * Comments containing "DEPRECATED", "TODO: Replace", "TO BE REMOVED", etc.
  * Code that is commented out or surrounded by conditional flags
  * References to newer replacement components
  * Lack of recent modifications or usage in the codebase
  * Import statements that use deprecated libraries or versions
- Default to "active" unless there is clear evidence of deprecation
- Consider the component's role in the overall architecture

last_modified should be the  timestamp of when you make any changes to the entry
---

## Output

Return the full updated mvcd.yaml file.

You must:
- Update the "description" field for entries with placeholders
- Add a new "confidence" field after the description field
- Add a new "status" field after the confidence field (either "active" or "deprecated")
- Leave all other fields (file, element, type, dependencies, loc) untouched
- Preserve the order and structure of all entries
- Maintain the top-level "codebase:" key
- Ensure the YAML remains valid and properly formatted

Example of an updated entry:
```yaml
- file: backend/app/services/mvcd_service.py
  element: MVCDService
  type: Utility
  description: "Service for scanning the codebase and generating structured metadata about code elements, preserving existing descriptions when re-scanning."
  confidence: 85
  status: active
  dependencies:
  - yaml
  - fnmatch
  loc: 218
  last_modified: 2025-05-21 12:00:00
```

---

## Constraints

- If you are unable to determine what an element does, leave the description as "TODO: Add description" and set confidence to 0
- Do not fabricate purpose or hallucinate functionality
- Do not reformat or reorder any part of the file
- Do not include comments or summaries — return only the updated mvcd.yaml content
- For entries that already have meaningful descriptions, add a confidence score but do not modify the description

---

## Constraints

- If you are unable to determine what an element does, leave the description as "TODO: Add description" and set confidence to 0
- Do not fabricate purpose or hallucinate functionality
- Do not reformat or reorder any part of the file
- Do not include comments or summaries — return only the updated mvcd.yaml content
- For entries with existing descriptions:
  * You may improve any description if you can provide a more accurate, clearer, or more complete description
  * Only replace existing descriptions when your new description would have a higher confidence score
  * Always recalculate the confidence score based on your current analysis
  * Never reduce the quality or specificity of existing descriptions
- For status determination:
  * Always add a status field (either "active" or "deprecated") for every entry
  * If an entry already has a status field, only change it if you find clear evidence contradicting the current status
  * Default to "active" unless there is clear evidence of deprecation
  * Be conservative in marking components as "deprecated" - only do so when there is strong evidence

## Final Instruction

Return only the updated mvcd.yaml file — no extra commentary, explanations, or markdown.
