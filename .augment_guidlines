# MindBack Development Guidelines

## Project Overview
- see spec

## Core Principles
- Deal with root causes, not symptoms - never use hardcoded fallbacks, validations, placeholders, or workarounds
- Never hardcode variables which should be delivered by the LLM
- Maintain independent workflows 
- When debugging, first present an analysis and wait for user feedback
- All documentation is now maintained in the documentation section
- This application runs on Windows using PowerShell
- when adding code which is replacing other code, the old code has to be marked as deprecated and obsolete

You are an expert in Python, TypeScript, React, FastAPI, JSON-based APIs, prompt engineering, and modern frontend-backend architecture.

# Dependency Management
- Always manage Python dependencies via `pip freeze > requirements.txt` and use a virtual environment.
- Remove unused packages to maintain lean requirements.
- Check for outdated packages using `pip list --outdated`, and validate installs with `pip check`.
- Use version pinning (e.g., `fastapi==0.100.1`) to avoid breaking changes.
- Automate updates using Dependabot or `pip-tools` (`pip-compile`).
- Before modifying `package.json`, review `dependency-notes.md`.

# Prompt Engineering
- Do NOT hardcode LLM prompts in source code.
- Store all prompts as YAML files 
- Do not change prompts unless clearly instructed by the user.
-
# Terminal Commands
- provide Windows PowerShell commands.
- Do not suggest commands for Mac or Linux—Windows only.

# Server ports
- ports 3000, 5173 and 8000 are not available, because they are used by other applications

# Coding Patterns and Structure
- Prioritize simple, maintainable solutions.
- Avoid hardcoded defaults and duplicated logic.
- Avoid files longer than 300 lines—modularize when expanding.
- Never mock or stub data in dev/prod—mocking is test-only.
- Never mask real error messages—report root causes directly.
- Do not overwrite `.env` files without confirmation from the user.
- Before adding new functionality, check for existing files with similar responsibilities.
- Reflect when the user reflects, and always ask if suggestions should be implemented.

# LLM Integration
- 
- ther shall be no llm model hardcoded, insted selectionbox in the UI
- all prompts are made in .yaml files in the prompt library

# Project Workflow
- Keep the documentation up-to-date 

# Code Style and Communication
- Do not use emojis unless explicitly asked.
- Always communicate decisions and ask for confirmation on implementation.

# Documentation Maintenance
- Update documentation when making significant code changes
- Keep documentation concise, accurate, and up-to-date
- Use Markdown for all documentation files
- Include code examples where appropriate
- Reference specific files and components using relative paths from the project root
