# Vibe Architect: Frontend Development - Project Loading and Structure

**Date: May 18, 2025**
**Updated: May 25, 2025** - UI color scheme changed to grayscale, removed timestamps and wave symbols
**Updated: May 19, 2025** - Removed old frontend directory to resolve conflicts with frontend-vite

## Overview

This document outlines the implementation of project loading functionality and the `.VibeArch` folder structure in the Vibe Architect application. The goal is to create a standardized way to store project metadata, specifications, architecture details, and documentation within each project directory.

## Project Loading Functionality

### Folder Loading Process

When a user selects a folder to load in the Vibe Architect application, the following process will be implemented:

1. **Folder Selection**:
   - User selects a folder through the UI
   - System validates folder accessibility

2. **`.VibeArch` Detection**:
   - Check if the selected folder contains a `.VibeArch` subdirectory
   - If not found, prompt user to initialize a new Vibe Architect project

3. **Project Initialization**:
   - If user confirms, create the `.VibeArch` directory and standard subdirectories
   - Generate initial configuration files
   - Display success message

4. **Project Loading**:
   - Load project metadata and configuration
   - Populate UI with project information
   - Enable project-specific functionality

### `.VibeArch` Folder Structure

The `.VibeArch` folder will contain the following subdirectories:

1. **Directory**: ✅
   - Contains project directory structure information
   - Stores file indexing data
   - Maintains directory scanning history

2. **Specification**: ✅
   - Stores project specifications in markdown format
   - Maintains version history of specifications
   - Links requirements to implementation

3. **Architecture**: ✅
   - Contains architecture diagrams and descriptions ✅
   - Stores component relationship information ✅
   - Maintains system design documentation ✅

4. **TechStack**: ✅
   - Lists technologies used in the project
   - Stores configuration for each technology
   - Maintains dependency information

5. **Documentation**: ✅
   - Contains project documentation
   - Stores generated documentation
   - Maintains documentation templates

## UI Implementation

### UI Design Guidelines

The Vibe Architect UI follows these design guidelines:

1. **Color Scheme**:
   - Uses grayscale color palette instead of blue
   - Primary colors: gray-500 to gray-900
   - Accent colors: gray-300 to gray-400
   - Text colors: gray-700 (primary), gray-500 (secondary)
   - No blue colors in any UI elements

2. **Header Design**:
   - Clean, minimal header with grayscale gradient background (from-gray-700 to-gray-900)
   - No timestamps or date indicators (removed from header)
   - Logo positioned on the left with app name and tagline
   - Tagline uses gray-300 instead of blue-200

3. **Navigation Elements**:
   - Sidebar uses dark gray background (gray-800) instead of blue-800
   - Active items highlighted with darker background (gray-900) instead of blue-900
   - Inactive items use lighter text (gray-300) instead of blue-100
   - Focus rings use gray-500 instead of blue-500

4. **Content Areas**:
   - White background with gray borders and shadows
   - Consistent padding and spacing
   - Clear visual hierarchy with headings in gray-900
   - Buttons and interactive elements use gray-600 instead of blue-600
   - No decorative wave elements or patterns

### Project Loading UI

The project loading UI will be enhanced with the following features:

1. **Folder Selection Dialog**:
   ```jsx
   const FolderSelector = () => {
     const [folderPath, setFolderPath] = useState('');
     const [isLoading, setIsLoading] = useState(false);

     const handleFolderSelect = async () => {
       setIsLoading(true);
       try {
         const response = await fetch('/api/projects/check', {
           method: 'POST',
           headers: { 'Content-Type': 'application/json' },
           body: JSON.stringify({ path: folderPath })
         });

         const data = await response.json();

         if (!data.hasVibeArchFolder) {
           // Prompt user to initialize
           if (confirm('Initialize Vibe Architect in this folder?')) {
             await initializeVibeArch(folderPath);
           }
         } else {
           // Load existing project
           loadProject(folderPath);
         }
       } catch (error) {
         console.error('Error checking folder:', error);
       } finally {
         setIsLoading(false);
       }
     };

     return (
       <div className="folder-selector">
         {/* UI implementation */}
       </div>
     );
   };
   ```

2. **Initialization Dialog**:
   - Displays when `.VibeArch` folder is not found
   - Provides options for initialization
   - Shows initialization progress

3. **Project Loading Indicator**:
   - Displays loading progress
   - Shows initialization steps
   - Provides error feedback if needed

### Project Navigation Tabs

The UI will include tabs corresponding to the `.VibeArch` folder structure:

1. **Directory Tab**: ❌
   - Displays project file structure
   - Provides file browsing functionality
   - Shows file metadata and statistics

2. **Specification Tab**: ❌
   - Displays project specifications
   - Provides specification editing
   - Shows specification history

3. **Architecture Tab**: ✅
   - Displays architecture diagrams ✅
   - Provides architecture editing tools ❌
   - Shows component relationships ✅

4. **TechStack Tab**: ❌
   - Displays technology stack
   - Provides technology configuration
   - Shows dependency information

5. **Documentation Tab**: ❌
   - Displays project documentation
   - Provides documentation editing
   - Shows documentation generation options

## Backend API Endpoints

To support the frontend functionality, the following API endpoints will be implemented:

1. **Check Project**:
   ```
   POST /api/projects/check
   Request: { path: string }
   Response: { exists: boolean, hasVibeArchFolder: boolean }
   ```

2. **Initialize Project**:
   ```
   POST /api/projects/initialize
   Request: { path: string, projectName: string }
   Response: { success: boolean, message: string }
   ```

3. **Load Project**:
   ```
   GET /api/projects/load
   Request: { path: string }
   Response: {
     name: string,
     path: string,
     structure: object,
     specification: string,
     architecture: object,
     techStack: object,
     documentation: object
   }
   ```

## Implementation Plan

### Phase 1: Backend Implementation

1. Create API endpoints for project checking, initialization, and loading
2. Implement folder structure creation logic
3. Develop file system interaction utilities
4. Add validation and error handling

### Phase 2: Frontend Implementation

1. Enhance folder selection UI
2. Implement initialization dialog
3. Create project loading indicators
4. Develop tab navigation structure

### Phase 3: Tab Content Implementation

1. Implement Directory tab functionality ❌
2. Develop Specification tab features ❌
3. Create Architecture tab visualization ✅
4. Build TechStack tab configuration ❌
5. Implement Documentation tab editing ❌

## File Structure Templates

### Initial `.VibeArch` Structure

```
.VibeArch/ ✅
├── Directory/ ✅
│   ├── index.json         # Directory structure index ❌
│   └── metadata.json      # Directory metadata ❌
├── Specification/ ✅
│   ├── main.md            # Main project specification ❌
│   └── history/           # Specification history ✅
├── Architecture/ ✅
│   ├── overview.md        # Architecture overview ❌
│   ├── diagrams/          # Architecture diagrams ✅
│   │   ├── system_architecture.md     # System architecture diagram ✅
│   │   ├── frontend_architecture.md   # Frontend architecture diagram ✅
│   │   ├── backend_architecture.md    # Backend architecture diagram ✅
│   │   └── data_flow.md               # Data flow diagram ✅
│   └── components.json    # Component definitions ❌
├── TechStack/ ✅
│   ├── stack.json         # Technology stack definition ❌
│   └── config/            # Technology configurations ✅
└── Documentation/ ✅
    ├── readme.md          # Project documentation ❌
    ├── api/               # API documentation ✅
    └── user/              # User documentation ✅
```

## Frontend Structure Cleanup

### Removal of Old Frontend Directory

The application previously contained two frontend implementations:
1. An older React application in the `frontend` directory (likely created with Create React App)
2. A newer Vite-based React application in the `frontend-vite` directory

This dual structure was causing conflicts, particularly with Node.js which is needed for MCP server browser tools. The run script was displaying a warning:

```
[2025-05-19 20:37:16] WARNING: Old 'frontend' directory detected!
[2025-05-19 20:37:16] This may cause conflicts with the new 'frontend-vite' directory.
[2025-05-19 20:37:16] Consider removing or renaming the old 'frontend' directory.
```

To resolve this issue, the old `frontend` directory has been completely removed, leaving only the Vite-based implementation. This change:
- Eliminates conflicts between the two frontend implementations
- Resolves issues with Node.js and MCP server browser tools
- Removes the warning message from the run script
- Simplifies the codebase structure

### MVCD Workflow Implementation

To support the Code Base navigation requirement, the Minimum Viable Code Description (MVCD) workflow has been implemented. This implementation includes:

1. **Backend API Endpoints**:
   - `GET /mvcd/status` - Get the status of the MVCD for a project
   - `POST /mvcd/generate` - Generate the initial MVCD structure
   - `POST /mvcd/enrich` - Enrich the MVCD with descriptions using a coding agent
   - `POST /mvcd/analyze` - Analyze the MVCD for improvement opportunities
   - `GET /mvcd/task/{task_id}` - Get the status of a running MVCD task

2. **Frontend Components**:
   - New MVCD React component with three tabs: Overview, Workflow, and Metrics
   - Status indicators for each step of the workflow
   - Manual trigger buttons for each step
   - Metrics visualization for frontend and backend code

3. **Workflow Process**:
   - **Step 1**: Analyze Codebase and Create MVCD - Scans the codebase to identify files, elements, dependencies, and LOC
   - **Step 2**: Enrich MVCD with Confidence Enhancement - Uses a coding agent to analyze code and generate meaningful descriptions
   - **Step 3**: Improvement Analysis - Analyzes the enriched MVCD to identify patterns and improvement opportunities

4. **Navigation Integration**:
   - Added MVCD route to the application
   - Updated sidebar to include a link to the MVCD page
   - Positioned between Directory and Tasks in the navigation

## Conclusion

The implementation of project loading functionality and the `.VibeArch` folder structure provides a standardized way to manage project metadata, specifications, architecture details, and documentation. This approach ensures that all project-related information is stored in a consistent location and can be easily accessed and modified through the Vibe Architect UI.

The tab-based navigation system in the frontend will provide intuitive access to different aspects of the project, making it easier for users to manage and understand their projects. The backend API endpoints will support these frontend features, ensuring a seamless user experience.

The removal of the old frontend directory and the implementation of the MVCD workflow further enhance the application's structure and capabilities, providing a more streamlined development experience and better code understanding.

## Next Steps

1. Implement the backend API endpoints for project management ❌
2. Develop the frontend UI components for project loading and initialization ❌
3. Create the tab navigation system and tab content components ✅ (Architecture tab complete, others pending)
4. Test the functionality with various project structures ❌
5. Refine the user experience based on feedback ❌

## Current Status

The implementation is partially complete:

- ✅ `.VibeArch` folder structure has been created
- ✅ Architecture tab has been implemented with diagram visualization
- ✅ System, Frontend, Backend, and Data Flow diagrams are working
- ✅ UI color scheme updated to grayscale
- ✅ Timestamps removed from UI elements
- ✅ Old frontend directory removed to resolve conflicts with frontend-vite
- ✅ MVCD workflow implementation added to support code base navigation
- ❌ Other tabs (Directory, Specification, TechStack, Documentation) are not yet implemented
- ❌ Project loading functionality is not yet implemented

See the 20250518_STAGE_Frontend.md document for detailed status information.
