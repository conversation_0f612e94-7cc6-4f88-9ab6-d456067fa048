# Vibe Architect: Meta-Level Responsibilities Specification

Vibe Architect (Vibearch) is not just a tool for writing code—it is a **meta-level orchestration system** designed to guide, structure, and moderate the development process when working with coding agents. Its purpose is to compensate for the typical shortcomings of these agents by managing the project lifecycle holistically.

---

## 1. Moderate the Specification of the Project

**Responsibilities:**

* Facilitate clear definition of project goals and intentions with the user.
* Capture and evolve project specs as markdown or YAML documents.
* Support iterative refinement: switch between fuzzy ideas and executable requirements.
* Distinguish between feature requirements, system constraints, and architectural patterns.

**Artifacts:**

* `project_spec.md`
* `intention.yaml`
* `requirements.yaml`

---

## 2. Manage the System Architecture

**Responsibilities:**

* Maintain exclusive architectural authority through the mvcd.yaml
* Maintain a high-level view of all modules, their relations, and boundaries
* Detect architectural drift or feature creep
* Visualize current module relationships
* Check that new features respect modular design principles
* Partition the codebase into isolated implementation tasks
* Define strict boundaries for each coding task
* Prevent architectural modifications by coding agents
* Validate all code changes against architectural constraints

**Artifacts:**

* `mvcd.yaml` - Private architectural blueprint used exclusively by VA
* `architecture_map.yaml` - High-level architectural representation
* Mermaid diagrams or DOT graphs - Visualizations of architecture
* `task_boundaries.yaml` - Defines implementation boundaries for coding tasks

**Architectural Authority Model:**

* 🧠 **Vibe Architect (VA)**:
  * The only agent with architectural authority
  * Holds and interprets the full mvcd.yaml
  * Responsible for breaking down work into precise, bounded coding tasks
  * Issues prompts with strict scope and constraints

* 🤖 **Coding Agents**:
  * Do not have architectural awareness
  * Are not collaborators, but task-bound executors
  * Receive minimal input: the exact function, file, line, or scope to work on
  * Are prevented from inventing, refactoring, or exploring beyond the assignment
  * Are stateless: they don't know what other agents are doing

* ⚙️ **Playwright**:
  * Is not a communication layer, but a command dispatcher
  * Simultaneously drives multiple isolated VS Code instances
  * Ensures physical and logical separation of agent tasks
  * Enforces a clean execution model (no agent-to-agent leakage)

* 🧩 **Role of mvcd.yaml**:
  * A private architectural blueprint used exclusively by VA to:
    * Determine what can be safely modified
    * Define precise boundaries of implementation
    * Avoid coupling/conflicts
    * Sequence and parallelize agent tasks

---

## 3. Assign Coding Tasks to Coding Agents

**Responsibilities:**

* Break down features into agent-executable coding prompts with strict boundaries
* Create highly specific, constrained prompts that prevent architectural modifications
* Store each prompt in a YAML format with explicit context, constraints, and file boundaries
* Ensure that prompts avoid duplication, are file-scoped unless specified otherwise
* Include explicit statements of what NOT to modify in each prompt
* Validate agent outputs against architectural constraints before integration
* Maintain a log of all agent assignments and their outcomes
* Sequence tasks to prevent conflicts between agents

**Artifacts:**

* `/prompts/*` (e.g. `generate_mvcd_service.yaml`) - Highly specific task prompts
* `agent_assignment_log.md` - Record of all agent tasks and outcomes
* `task_validation_rules.yaml` - Rules for validating agent outputs

**Security & Constraint Principles:**

* **Need-to-know principle** — agents only see what they must
* **Immutability of architecture** — agents cannot modify architectural plans
* **Prompt-as-contract** — each prompt is a legal contract; not just a suggestion
* **Zero-Trust execution model** — all agent outputs are validated before acceptance

**Prompt Structure:**

* **Task Definition**: Precise description of what to implement
* **File Scope**: Exact files and line numbers that may be modified
* **Constraints**: Explicit boundaries and limitations
* **Validation Criteria**: How the output will be evaluated
* **Rejection Conditions**: Circumstances under which output will be rejected

---

## 4. Moderate the Testing of the Code

**Responsibilities:**

* Detect missing test coverage for functions, services, or endpoints.
* Create initial `pytest` or `jest` scaffolds.
* Assign agents to write specific test cases.
* Maintain mapping between implementation and corresponding test coverage.

**Artifacts:**

* `test_map.yaml`
* `/tests/` folder tree

---

## 5. Moderate the Documentation of the Code

**Responsibilities:**

* Track which functions/classes/modules lack docstrings or file-level documentation.
* Assign documentation prompts to agents.
* Moderate semantic quality: detect vacuous or vague docstrings.
* Generate overview docs (e.g. `README.md`, `/docs/structure.md`, module guides).

**Artifacts:**

* `doc_coverage_report.md`
* `/docs/*`

---

## 6. Maintain Version Control

**Responsibilities:**

* Moderate the Git commit process.
* Validate commit messages for clarity and traceability.
* Group related changes into logical commits.
* Prevent committing broken or untested code.
* Optionally facilitate pull request descriptions and changelogs.

**Artifacts:**

* `commit_plan.yaml`
* `changelog.md`

---

## 7. Enforce Security Standards

**Responsibilities:**

* Prevent insecure architectural patterns (e.g., disallowing direct LLM calls from the frontend).
* Validate `.env` and config file placements.
* Ensure sensitive data is never hardcoded.
* Flag insecure dependencies or API usages.

**Artifacts:**

* `security_audit.md`
* `security_rules.yaml`

---

## 8. Ensure Regulatory Compliance

**Responsibilities:**

* Monitor and guide adherence to relevant regulations (e.g., EU AI Act, DSGVO).
* Track use of personal data and ensure proper consent handling.
* Enable explainability and fairness checks when using AI components.

**Artifacts:**

* `compliance_register.yaml`
* `privacy_policy.md`

---

## Meta-Behavior: Multi-Agent Orchestration

Vibearch behaves as a **centralized orchestration system** with strict control over multiple coding agents:

* Maintains exclusive architectural authority through mvcd.yaml
* Decomposes work into isolated, bounded implementation tasks
* Dispatches highly specific, constrained prompts to coding agents
* Validates all agent outputs against architectural constraints
* Prevents architectural drift by strictly controlling what agents can modify
* Logs all actions, decisions, and rationale in structured form
* Creates an audit trail of all agent activity

**Orchestration Model:**

* **Top-Down Control**: VA maintains complete control over the development process
* **Strict Isolation**: Agents work on isolated tasks with no knowledge of other agents
* **Zero-Trust Execution**: All agent outputs are validated before acceptance
* **Architectural Integrity**: Architecture is defined and maintained solely by VA

**Primary Modes:**

* **Interactive Mode**: User guides the architectural decisions, VA orchestrates implementation
* **Autopilot Mode**: VA autonomously orchestrates implementation within predefined architectural boundaries

**Strategic Strengths:**

* **Scalability**: Multiple agents can work in parallel with no code collision
* **Security**: Architecture is resistant to overreach, hallucination, or drift
* **Accountability**: Each change can be traced to its source task, prompt, and agent
* **Correctability**: Agent outputs can be tested, validated, or rejected before integration

---

## 9. Monitor and Visualize System Health

**Responsibilities:**

* Track code quality metrics and documentation status
* Generate visualizations of system architecture
* Identify potential bottlenecks or areas of concern
* Provide a comprehensive dashboard for system health monitoring
* Display confidence scores and documentation quality metrics
* Track codebase statistics (LOC, component count)
* Integrate with Git to show changes and trends
* Provide a component explorer with filtering and sorting
* Display improvement suggestions from analysis

**Artifacts:**

* `/visualizations/*` - Architecture and relationship diagrams
* `system_health_dashboard.html` - Main dashboard interface
* `metrics_log.yaml` - Historical metrics data
* `.VibeArch/Improvement/*.yaml` - Improvement suggestions

**Three-Step Workflow UI:**

* **Step 1: Analyze Codebase and Create MVCD**
  * Status indicator (red/green dot)
  * Manual trigger button
  * Progress indicator when running

* **Step 2: Enrich MVCD with Confidence Enhancement**
  * Status indicator (red/green dot)
  * Manual trigger button
  * Progress indicator when running

* **Step 3: Improvement Analysis**
  * Status indicator (red/green dot)
  * Manual trigger button
  * Progress indicator when running

**Dashboard Components:**

* Codebase metrics (LOC, component count)
* Documentation quality (confidence scores, coverage)
* Git integration (changes, history)
* Component explorer (scrollable list with metrics)
* Improvement suggestions (categorized list)

---

## Future Extensions

* CICD Pipeline Moderation
* Security & Compliance Checks
* Refactoring Proposal System
* Agent Performance Benchmarking
