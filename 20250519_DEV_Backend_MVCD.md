# Minimum Viable Code Description (MVCD) System

## Overview

The MVCD system provides a structured approach to documenting and understanding the codebase through an iterative process that combines automated scanning with LLM-powered semantic analysis. This document outlines the architecture, workflow, and implementation details for the MVCD system.

## Core Components

1. **MVCDService**: Backend service that scans the codebase to generate structural metadata
2. **Prompt Templates**: Structured instructions for the coding agent to analyze and enrich descriptions
3. **Git Integration**: Version control for tracking documentation evolution
4. **Confidence Scoring**: Mechanism to prioritize areas needing improvement

## Workflow

### Initial Generation

1. MVCDService scans the codebase to identify:
   - Files and their elements (classes, functions, components)
   - Element types (Component, Hook, Utility, etc.)
   - Dependencies (external libraries)
   - Lines of code (LOC)

2. For new entries, MVCDService adds placeholder descriptions:
   - `"TODO: Add description"`

3. The output is saved to `.VibeArch/Directory/mvcd.yaml`

### Iterative Enrichment

1. A coding agent (LLM) is prompted with `mvcd_description_enrichment_prompt.md`
2. The agent:
   - Reads the existing mvcd.yaml file
   - Analyzes code files with missing descriptions
   - Generates meaningful descriptions
   - Assigns confidence scores
   - Updates the mvcd.yaml file

3. The updated file is committed to Git, preserving history

4. This process repeats periodically as the codebase evolves

## Implementation Details

### MVCDService

The MVCDService is responsible for:
- Walking the codebase to find relevant files
- Parsing files to extract metadata
- Detecting element types and dependencies
- Preserving existing descriptions when re-scanning
- Outputting a structured YAML file

### Prompt Templates

Two key prompts guide the process:

1. **Initial_Minimum_Viable_Code_Description.md**:
   - Instructs the coding agent on the initial scanning process
   - Defines the YAML schema and output format
   - Specifies ignore rules for irrelevant files

2. **mvcd_description_enrichment_prompt.md**:
   - Guides the agent in analyzing code and generating descriptions
   - Specifies description format and constraints
   - Includes instructions for confidence scoring
   - Provides guidelines for determining component status (active/deprecated)
   - Explains how to identify deprecated components based on code patterns

### Confidence Scoring

Each entry in the mvcd.yaml will include a confidence score:
- Scale: 0-100%
- Indicates the LLM's confidence in the accuracy of the description
- Based on code clarity, comments, naming conventions, and consistency
- Helps prioritize entries for further review or refinement

Example entry with confidence score and status:
```yaml
- file: backend/app/services/mvcd_service.py
  element: MVCDService
  type: Utility
  description: "Service for scanning the codebase and generating structured metadata about code elements, preserving existing descriptions when re-scanning."
  confidence: 85
  status: active  # Options: active, deprecated
  dependencies:
  - yaml
  - fnmatch
  loc: 218
```

### Git Integration

The mvcd.yaml file will be tracked in Git:
- Regular commits capture documentation evolution
- Commit messages explain what was updated and why
- Git history provides a timeline of how understanding evolves
- No need for custom versioning system

## Automated Enrichment with AutoHotKey

To automate the MVCD enrichment process, we will implement an AutoHotKey-based solution that:

1. Runs the MVCDService to generate/update the structure
2. Copies the prompt to the clipboard
3. Uses keyboard automation to paste the prompt into the coding agent's chat interface
4. Monitors the MVCD file for changes to track progress

### Implementation Approach

```ahk
; augment_mvcd_automation.ahk

; Read the prompt from file
FileRead, PromptText, .VibeArch\VibeArch_Setup\mvcd_description_enrichment_prompt.yaml
if ErrorLevel {
    FileAppend, Error: Could not read prompt file`n, automation_log.txt
    ExitApp, 1
}

; Copy to clipboard
Clipboard := PromptText

; Activate VSCode window
WinActivate, ahk_exe Code.exe
if ErrorLevel {
    FileAppend, Error: Could not activate VSCode`n, automation_log.txt
    ExitApp, 1
}

; Open Augment chat
Send, ^+p
Sleep, 300
Send, Augment: Open Chat
Send, {Enter}
Sleep, 1000

; Paste and send prompt
Send, ^v
Sleep, 100
Send, {Enter}

; Log success and exit
FileAppend, Success: Prompt inserted into Augment chat`n, automation_log.txt
ExitApp, 0
```

### Python Helper Script

```python
import os
import subprocess
import time
from pathlib import Path

def automate_mvcd_enrichment(project_path):
    """
    Automate the MVCD enrichment process using AutoHotKey.

    Args:
        project_path: Path to the project root
    """
    # 1. Run the MVCDService to generate/update the structure
    from app.services.mvcd_service import MVCDService
    mvcd_service = MVCDService(project_path)
    mvcd_service.generate()

    # 2. Run the AutoHotKey script to insert the prompt
    ahk_script_path = os.path.join(project_path, "scripts", "augment_mvcd_automation.ahk")
    subprocess.run(["AutoHotkey.exe", ahk_script_path], check=True)

    # 3. Start monitoring the MVCD file for changes
    mvcd_path = os.path.join(project_path, ".VibeArch", "Directory", "mvcd.yaml")
    last_modified = os.path.getmtime(mvcd_path)

    print("Prompt inserted into Augment chat. Monitoring MVCD file for changes...")

    # Return immediately - the backend will monitor the file in a separate thread
    return True
```

### Implementation Considerations

1. **Keyboard and Clipboard Automation**:
   - Uses the most universal interface (keyboard and clipboard) that works across applications
   - No dependency on specific UI elements, DOM structures, or application internals
   - Works regardless of whether the target is a native app, Electron app, or web app

2. **Minimal Dependencies**:
   - AutoHotKey is lightweight and purpose-built for exactly this kind of automation
   - No need for complex browser automation frameworks
   - No additional servers or services to maintain

3. **Resilience to Change**:
   - UI updates in VSCode or Augment won't break the automation
   - No reliance on CSS selectors or DOM structures that might change
   - Works even if the extension's internal implementation changes completely

4. **Progress Monitoring**:
   - Backend monitors the MVCD file for changes to track progress
   - Updates the UI based on file changes rather than trying to extract information from the chat interface
   - Provides real-time feedback on the enrichment process

5. **Error Handling**:
   - Simple error handling focused on the automation script itself
   - Logs errors to a file for debugging
   - Backend can detect if the automation fails to start or complete

6. **Integration with Development Workflow**:
   - Can be triggered from the web UI via backend API
   - Can be scheduled to run periodically
   - Can be integrated with Git hooks for automatic execution

### Integration with Development Workflow

The automated enrichment process can be integrated into the development workflow in several ways:

1. **Web UI Trigger**: User clicks a button in the Vibe Architect interface to start the process
2. **Scheduled Job**: Run nightly to keep documentation current
3. **Git Hook**: Run after code changes are committed
4. **Manual Script**: Run on demand from the command line

## Three-Step MVCD Workflow

The MVCD system follows a three-step workflow, each with user control and visual status indicators:

1. **Analyze Codebase and Create MVCD**:
   - Scans the codebase to identify files, elements, dependencies, and LOC
   - Generates the basic structure with placeholders for descriptions
   - Status indicator (red/green dot) shows completion status
   - Manual trigger button allows user to initiate the process

2. **Enrich MVCD with Confidence Enhancement**:
   - Uses a coding agent to analyze code and generate meaningful descriptions
   - Assigns confidence scores to each description
   - Iteratively improves descriptions with each run
   - Status indicator (red/green dot) shows completion status
   - Manual trigger button allows user to initiate the process

3. **Improvement Analysis**:
   - Analyzes the enriched MVCD to identify patterns and improvement opportunities
   - Generates improvement suggestions in `.VibeArch/Improvement`
   - Requires user confirmation before execution
   - Status indicator (red/green dot) shows completion status
   - Manual trigger button allows user to initiate the process

This workflow provides a clear progression from documentation to analysis while maintaining user control at each step.

## Dashboard Metrics

The MVCD system generates metrics that are displayed in the Vibe Architect dashboard:

1. **Codebase Metrics**:
   - Total lines of code (Frontend, Backend)
   - Component count by type
   - Dependencies and relationships
   - Active vs. deprecated component counts

2. **Documentation Quality**:
   - Overall confidence level (Frontend, Backend)
   - Documentation coverage percentage
   - Components with low confidence scores
   - Status distribution (active/deprecated)

3. **Git Integration**:
   - Number of changes since last analysis
   - Modified files and components
   - Commit history and trends

4. **Component Explorer**:
   - Scrollable list of components with LOC, confidence scores, and status indicators
   - Filtering and sorting options (by type, status, confidence, etc.)
   - Visual indicators for active/deprecated status (green/red badges)
   - Detailed component information with status history

For detailed information on dashboard metrics and UI, see [20250520_DEV_Dashboard_Metrics_and_UI.md](20250520_DEV_Dashboard_Metrics_and_UI.md).

## Next Steps

1. **Implement Automated Enrichment**:
   - Develop the AutoHotKey script for interacting with coding agents
   - Create the Python helper script for integration with the backend
   - Implement file monitoring for progress tracking
   - Test with different coding agent interfaces (Augment, Cursor)

2. **Implement Three-Step Workflow**:
   - Create UI components for process status and controls
   - Implement manual trigger functionality
   - Add visual indicators for process status
   - Enhance progress reporting based on file changes

3. **Develop Dashboard Metrics**:
   - Implement metrics calculation from MVCD data
   - Create visualizations for confidence scores and LOC
   - Integrate with Git for change tracking
   - Add real-time progress indicators during enrichment

4. **Create Improvement Analysis**:
   - Develop algorithms to identify code duplication and patterns
   - Implement suggestion generation and categorization
   - Create storage structure in `.VibeArch/Improvement`
   - Integrate with the enrichment process for continuous improvement

## Benefits

- **Progressive Understanding**: Each iteration deepens the system's self-documentation
- **Prioritized Improvement**: Confidence scores focus efforts where most needed
- **Historical Context**: Git history tracks how documentation evolves
- **Separation of Concerns**: Technical scanning vs. semantic understanding

## Strategic Role in Multi-Agent Orchestration

The mvcd.yaml file plays a critical role in Vibe Architect's multi-agent orchestration model:

### Architectural Authority

1. **Private Blueprint**:
   - mvcd.yaml is a private architectural blueprint used exclusively by VA
   - Coding agents never receive the full architectural context
   - Only VA has the complete understanding of the system architecture

2. **Task Boundary Definition**:
   - VA uses mvcd.yaml to identify components that can be safely modified in isolation
   - It defines precise implementation boundaries for coding tasks
   - This enables safe task allocation without architectural leakage

3. **Conflict Prevention**:
   - Dependency information in mvcd.yaml helps prevent multiple agents from modifying tightly coupled code
   - VA can analyze the impact radius of changes to avoid conflicts
   - This enables parallel development without code collisions

### Zero-Trust Execution Model

1. **Need-to-Know Principle**:
   - Coding agents only receive the minimal information needed for their specific task
   - They don't "understand" the broader architecture
   - They have no visibility into other agents' work or responsibilities

2. **Immutability of Architecture**:
   - Coding agents cannot modify architectural boundaries
   - VA maintains exclusive control over architectural decisions
   - All changes are validated against architectural constraints

3. **Prompt-as-Contract**:
   - VA creates highly specific, constrained prompts based on mvcd.yaml
   - These prompts include explicit statements of what NOT to modify
   - They focus on "how" to implement rather than "what" to design

### Scalable Parallel Development

1. **Multiple Agents, One Architecture**:
   - VA can orchestrate multiple coding agents working in parallel
   - Each agent works on isolated tasks with clear boundaries
   - The central architectural authority prevents conflicts

2. **Task Sequencing and Prioritization**:
   - VA uses mvcd.yaml to determine the optimal sequence of tasks
   - Dependencies between components inform task ordering
   - This maximizes parallel development while minimizing conflicts

3. **Validation and Integration**:
   - All agent outputs are validated against the architectural model
   - Changes that violate boundaries are rejected
   - This ensures architectural integrity is maintained

## Conclusion

The MVCD system represents a sophisticated approach to codebase documentation and architectural governance. By maintaining a comprehensive, structured representation of the codebase, Vibe Architect can effectively orchestrate multiple coding agents working in parallel while preserving architectural integrity.

The mvcd.yaml file is not just documentation - it's the central nervous system of a multi-agent development ecosystem, enabling VA to coordinate specialized agents to work together on a cohesive whole without architectural drift or code conflicts. Its growth reflects the system's increasing sophistication and self-awareness, providing the foundation for scalable, parallel development with strict architectural governance.