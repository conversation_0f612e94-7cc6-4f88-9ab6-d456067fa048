# Vibe Architect

An orchestrator tool that manages and coordinates AI-based coding agents while maintaining structural clarity and code quality in a single repository.

## Features

- Select and supervise a project directory
- Interface with LLMs and coding tools
- Track project evolution through a maintained `.md` spec
- Maintain a development task list ([todo], [done])
- Offer a visual representation of workflow structure (graph-based UI)

## Tech Stack

- **Backend**: FastAPI, Python 3.11+, NetworkX, Matplotlib
- **Frontend**: Vite + React (modern best practice for 2024-2025), Tailwind CSS, D3.js
- **Storage**: File-based (YAML and Markdown)

> **Note**: The project has been migrated from Create React App (CRA) to Vite + React, which is the modern best practice for 2024-2025.

## Getting Started

### Prerequisites

- Python 3.11 or higher
- Node.js 20 or higher (recommended for Vite)
- npm (should be installed with Node.js)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/vibe-architect.git
   cd vibe-architect
   ```

2. Create a `.env` file from the example:
   ```bash
   cp .env.example .env
   ```

3. Edit the `.env` file to add your API keys.

### Running the Application

#### Option 1: Using the Run Script (Recommended)

Run both backend and frontend with a single command:
```powershell
# On Windows with Vite + React (recommended)
.\run-vite.ps1

# On Windows with legacy Create React App
.\run.ps1
```

This script will:
- Check for required dependencies (Python, Node.js, npm)
- Install all necessary packages
- Start both the backend and frontend servers in separate windows
- Open the application in your default browser

> **Note**: We recommend using `run-vite.ps1` for the modern Vite + React frontend.

#### Option 2: Manual Setup

1. Set up the Python environment:
   ```powershell
   # Add the project root to PYTHONPATH
   $env:PYTHONPATH = "$PWD;$env:PYTHONPATH"

   # Install backend in development mode
   cd backend
   python -m pip install -r requirements.txt
   python -m pip install -e .
   cd ..

   # Start the backend server
   python -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8080 --reload
   ```

2. In a new terminal window, set up the frontend:
   ```powershell
   # Add npm to PATH if needed
   $env:PATH += ";C:\Program Files\nodejs"

   # Set up frontend environment
   cd frontend
   npm install

   # Start the frontend server (using port 6000 to avoid conflicts)
   set PORT=6000
   npm start
   ```

### Running the Application on macOS/Linux

1. Set up the Python environment:
   ```bash
   # Add the project root to PYTHONPATH
   export PYTHONPATH=$PWD:$PYTHONPATH

   # Install backend in development mode
   cd backend
   python -m pip install -r requirements.txt
   python -m pip install -e .
   cd ..

   # Start the backend server
   python -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8080 --reload
   ```

2. In a new terminal window, set up the frontend:
   ```bash
   cd frontend
   npm install

   # Start the frontend server (using port 6000 to avoid conflicts)
   PORT=6000 npm start
   ```

## Usage

### Project Management

1. Set your base directory to scan for projects
2. Create a new project or select an existing one
3. View and edit project settings

### Task Management

1. Add tasks to your project
2. Mark tasks as complete
3. View task dependencies

### LLM Integration

1. Configure your preferred LLM provider
2. Send prompts to the LLM
3. Use LLM for code generation and task planning

### Visualization

1. View a graph representation of your project structure
2. Export graphs as PNG or SVG

## Troubleshooting

### Port Conflicts

If you encounter port conflicts, you can use the `kill-ports.ps1` script to free up the required ports:

```powershell
.\kill-ports.ps1
```

### Backend Issues

- **Module Not Found Error**: Make sure to set the PYTHONPATH environment variable to include the project root directory.
- **Import Errors**: Ensure you've installed the backend package in development mode with `pip install -e .` from the backend directory.

### Frontend Issues

- **npm Not Found**: Make sure Node.js is installed and npm is in your PATH. On Windows, you may need to add `C:\Program Files\nodejs` to your PATH.
- **react-scripts Not Found**: This is only relevant for the legacy CRA frontend. Try reinstalling the frontend dependencies with `npm ci` or `npm install`.
- **Vite Issues**: For the modern frontend, make sure you're using Node.js 20+ and have installed all dependencies with `npm install` in the `frontend-vite` directory.

## Development

### MVCD (Minimum Viable Code Description)

Vibe Architect includes a system for automatically documenting the codebase:

1. **Generate MVCD File**:
   ```bash
   python test_mvcd_service.py
   ```
   This scans the codebase and creates a structured YAML file at `.VibeArch/Directory/mvcd.yaml` with metadata about all code elements.

2. **Enrich Descriptions with Coding Agent**:
   ```bash
   python augment_mvcd_helper.py
   ```
   This helps you interact with a coding agent (like Augment) to analyze the code and add meaningful descriptions with confidence scores.

3. **Options**:
   ```bash
   python augment_mvcd_helper.py --prompt ".VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml" --output ".VibeArch/Directory/mvcd.yaml"
   ```
   - `--prompt`: Path to the prompt file
   - `--output`: Path to save the response

4. **Automated Enrichment** (requires AutoHotKey):
   ```powershell
   .\scripts\augment_mvcd_automation.ahk
   ```
   This script automates the interaction with the coding agent by:
   - Reading the prompt from the file
   - Copying it to the clipboard
   - Activating VSCode
   - Opening the Augment chat
   - Pasting the prompt and sending it

The MVCD system provides:
- Comprehensive documentation of all code elements
- Confidence scores to prioritize areas needing improvement
- Component status tracking (active/deprecated)
- Integration with Git for tracking documentation evolution
- Automated enrichment through coding agent analysis
- Real-time progress tracking through file monitoring
- Visual indicators for component status in the UI

### Backend

The backend is built with FastAPI and uses file-based storage:

```bash
cd backend
pip install -r requirements.txt
pip install -e .
cd ..
python -m uvicorn backend.app.main:app --port 8080 --reload
```

### Frontend

#### Modern Frontend (Vite + React)

The modern frontend is built with Vite, React, and Tailwind CSS:

```bash
cd frontend-vite
npm install
npm run dev -- --port 5000
```

#### Legacy Frontend (Create React App)

The legacy frontend is built with Create React App and Tailwind CSS:

```bash
cd frontend
npm install
PORT=6000 npm start
```

## License

MIT
