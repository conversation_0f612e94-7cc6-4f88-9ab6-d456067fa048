"""
Augment MVCD Helper

This script helps with the MVCD enrichment process for the Augment coding agent.
It provides two modes of operation:
1. Manual mode: Copies the prompt to the clipboard and guides the user through the process
2. Automated mode: Uses AutoHotKey to automate the interaction with Augment

Usage:
    python augment_mvcd_helper.py [--auto] [--prompt PATH] [--output PATH] [--monitor]

Options:
    --auto      Use AutoHotKey for automation (requires AutoHotKey installed)
    --prompt    Path to the prompt file
    --output    Path to save the response
    --monitor   Monitor the output file for changes (only with --auto)

This script replaces the previous Playwright-based approach with a more reliable
and simpler implementation using AutoHotKey for automation.
"""

import os
import sys
import time
import argparse
import yaml
import subprocess
import threading
import traceback
from pathlib import Path

try:
    import pyperclip
except ImportError:
    print("Error: pyperclip not installed. Install with: pip install pyperclip")
    sys.exit(1)

def load_prompt(prompt_path):
    """Load the prompt from the specified file."""
    try:
        with open(prompt_path, "r", encoding="utf-8") as f:
            prompt = f.read()
        print(f"Loaded prompt ({len(prompt)} characters) from {prompt_path}")
        return prompt
    except Exception as e:
        print(f"Error loading prompt: {e}")
        return None

def save_response(response_path):
    """Save the response to the specified file."""
    try:
        response = pyperclip.paste()

        # Check if the response starts with "codebase:"
        if not response.strip().startswith("codebase:"):
            print("Error: The clipboard content doesn't appear to be a valid MVCD YAML response.")
            print("Make sure you've copied the entire response from Augment, starting with 'codebase:'")
            return False

        # Save the response
        with open(response_path, "w", encoding="utf-8") as f:
            f.write(response)

        print(f"Saved response to {response_path}")
        return True
    except Exception as e:
        print(f"Error saving response: {e}")
        return False

def monitor_file_changes(file_path, initial_timestamp=None):
    """
    Monitor a file for changes and print updates.

    Args:
        file_path: Path to the file to monitor
        initial_timestamp: Initial modification timestamp

    Returns:
        True if the file was modified, False otherwise
    """
    file_path = Path(file_path)
    if not file_path.exists():
        print(f"Warning: File {file_path} does not exist. Cannot monitor.")
        return False

    if initial_timestamp is None:
        initial_timestamp = file_path.stat().st_mtime

    print(f"Monitoring {file_path} for changes...")
    print("Press Ctrl+C to stop monitoring.")

    try:
        last_size = file_path.stat().st_size

        while True:
            time.sleep(2)  # Check every 2 seconds

            if not file_path.exists():
                print(f"Warning: File {file_path} was deleted.")
                return False

            current_timestamp = file_path.stat().st_mtime
            current_size = file_path.stat().st_size

            if current_timestamp > initial_timestamp:
                print(f"File updated at {time.ctime(current_timestamp)}")

                # If the file size changed, print the new size
                if current_size != last_size:
                    size_diff = current_size - last_size
                    print(f"File size changed: {size_diff:+d} bytes (now {current_size} bytes)")
                    last_size = current_size

                # Try to load the file as YAML to check if it's valid
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        yaml_content = yaml.safe_load(f)

                    if yaml_content and 'codebase' in yaml_content:
                        entries = yaml_content['codebase']
                        entries_with_descriptions = sum(1 for entry in entries if entry.get('description') and entry.get('description') != 'TODO: Add description')
                        entries_with_confidence = sum(1 for entry in entries if 'confidence' in entry and entry['confidence'] > 0)

                        print(f"MVCD file updated: {len(entries)} entries total, {entries_with_descriptions} with descriptions, {entries_with_confidence} with confidence scores")

                        # If all entries have descriptions and confidence scores, we're done
                        if entries_with_descriptions == len(entries) and entries_with_confidence == len(entries):
                            print("\nAll entries have descriptions and confidence scores!")
                            print("MVCD enrichment complete.")
                            return True
                except Exception as e:
                    print(f"Warning: Could not parse YAML file: {e}")

    except KeyboardInterrupt:
        print("\nMonitoring stopped by user.")
        return False

def is_autohotkey_installed():
    """
    Check if AutoHotKey is installed.

    Returns:
        True if AutoHotKey is installed, False otherwise
    """
    # IMPORTANT: Since we know AutoHotkey is installed (user confirmed),
    # we'll return True directly to bypass the detection issues
    print("AutoHotkey detection bypassed - assuming it's installed as confirmed by user")
    return True

    # The code below is kept for reference but not used
    """
    # Common installation paths for AutoHotkey
    common_paths = [
        "C:\\Program Files\\AutoHotkey\\AutoHotkey.exe",
        "C:\\Program Files (x86)\\AutoHotkey\\AutoHotkey.exe",
        os.path.expanduser("~\\AppData\\Local\\Programs\\AutoHotkey\\AutoHotkey.exe"),
    ]

    try:
        # First try to find AutoHotKey in the PATH
        result = subprocess.run(["where", "AutoHotkey.exe"], capture_output=True, text=True)
        if result.returncode == 0:
            return True

        # If not in PATH, check common installation locations
        for path in common_paths:
            if os.path.exists(path):
                print(f"Found AutoHotkey at: {path}")
                return True

        # Also check if AutoHotkeyU64.exe or AutoHotkeyU32.exe exists
        alt_result = subprocess.run(["where", "AutoHotkeyU64.exe"], capture_output=True, text=True)
        if alt_result.returncode == 0:
            print("Found AutoHotkeyU64.exe in PATH")
            return True

        alt_result = subprocess.run(["where", "AutoHotkeyU32.exe"], capture_output=True, text=True)
        if alt_result.returncode == 0:
            print("Found AutoHotkeyU32.exe in PATH")
            return True

        return False
    except Exception as e:
        print(f"Error checking for AutoHotkey: {e}")
        return False
    """

def get_autohotkey_path():
    """
    Get the path to the AutoHotKey executable.

    Returns:
        Path to AutoHotKey executable or None if not found
    """
    # Since we know AutoHotkey is installed, we'll use "AutoHotkey.exe" directly
    # This will work if AutoHotkey is in the PATH
    print("Using 'AutoHotkey.exe' directly from PATH")
    return "AutoHotkey.exe"

    # The code below is kept for reference but not used
    """
    # Common installation paths for AutoHotkey
    common_paths = [
        "C:\\Program Files\\AutoHotkey\\AutoHotkey.exe",
        "C:\\Program Files (x86)\\AutoHotkey\\AutoHotkey.exe",
        os.path.expanduser("~\\AppData\\Local\\Programs\\AutoHotkey\\AutoHotkey.exe"),
    ]

    # First check if it's in the PATH
    try:
        result = subprocess.run(["where", "AutoHotkey.exe"], capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            return result.stdout.strip().split('\n')[0]
    except:
        pass

    # Check common installation locations
    for path in common_paths:
        if os.path.exists(path):
            return path

    # Check for alternative versions
    try:
        alt_result = subprocess.run(["where", "AutoHotkeyU64.exe"], capture_output=True, text=True)
        if alt_result.returncode == 0 and alt_result.stdout.strip():
            return alt_result.stdout.strip().split('\n')[0]
    except:
        pass

    try:
        alt_result = subprocess.run(["where", "AutoHotkeyU32.exe"], capture_output=True, text=True)
        if alt_result.returncode == 0 and alt_result.stdout.strip():
            return alt_result.stdout.strip().split('\n')[0]
    except:
        pass

    return None
    """

def run_autohotkey_script(script_path):
    """
    Run an AutoHotKey script.

    Args:
        script_path: Path to the AutoHotKey script

    Returns:
        True if the script ran successfully, False otherwise
    """
    try:
        # Convert to absolute path
        abs_script_path = os.path.abspath(script_path)
        print(f"Absolute script path: {abs_script_path}")

        # Check for common AutoHotkey v2 paths first (preferred)
        autohotkey_paths = [
            "C:\\Program Files\\AutoHotkey\\v2\\AutoHotkey64.exe",
            "C:\\Program Files\\AutoHotkey\\v2\\AutoHotkey32.exe",
            # Then check v1 paths
            "C:\\Program Files\\AutoHotkey\\AutoHotkey.exe",
            "C:\\Program Files\\AutoHotkey\\v1\\AutoHotkey.exe",
            "C:\\Program Files (x86)\\AutoHotkey\\AutoHotkey.exe"
        ]

        autohotkey_path = None
        for path in autohotkey_paths:
            if os.path.exists(path):
                autohotkey_path = path
                break

        if not autohotkey_path:
            # Last resort: try to find in PATH
            try:
                result = subprocess.run(["where", "AutoHotkey.exe"], capture_output=True, text=True, check=False)
                if result.returncode == 0 and result.stdout.strip():
                    autohotkey_path = result.stdout.strip().split('\n')[0]
            except:
                pass

        if not autohotkey_path:
            print("ERROR: Could not find AutoHotkey executable. Please ensure it's installed.")
            return False

        print(f"Using AutoHotKey at: {autohotkey_path}")

        # Direct command execution with full path
        print(f"Running AutoHotKey script: {abs_script_path}")

        # Create command with proper quoting
        command = f'"{autohotkey_path}" "{abs_script_path}"'
        print(f"Executing command: {command}")

        # Run the command with shell=True to handle paths with spaces
        result = subprocess.run(command, shell=True)

        # Check for debug log regardless of success/failure
        debug_log_path = Path("autohotkey_debug.log")
        if debug_log_path.exists():
            print("\n=== AutoHotKey Debug Log ===")
            with open(debug_log_path, "r", encoding="utf-8") as f:
                log_content = f.read()
                print(log_content)
            print("=== End Debug Log ===\n")

        if result.returncode == 0:
            print("AutoHotKey script ran successfully.")
            return True
        else:
            print(f"AutoHotKey script failed with return code {result.returncode}")
            return False
    except Exception as e:
        print(f"Error running AutoHotKey script: {e}")
        traceback.print_exc()  # Print full traceback for debugging

        # Try to check for debug log even on exception
        try:
            debug_log_path = Path("autohotkey_debug.log")
            if debug_log_path.exists():
                print("\n=== AutoHotKey Debug Log (Error) ===")
                with open(debug_log_path, "r", encoding="utf-8") as f:
                    log_content = f.read()
                    print(log_content)
                print("=== End Debug Log ===\n")
        except:
            pass

        return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Augment MVCD Helper")
    parser.add_argument("--auto", action="store_true", help="Use AutoHotKey for automation")
    parser.add_argument("--prompt", help="Path to the prompt file",
                        default=".VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml")
    parser.add_argument("--output", help="Path to save the response",
                        default=".VibeArch/Directory/mvcd.yaml")
    parser.add_argument("--monitor", action="store_true", help="Monitor the output file for changes")
    parser.add_argument("--delay", type=int, default=0, help="Delay in seconds before running the automation (default: 0)")
    parser.add_argument("--force", action="store_true", help="Force automation without any warnings or checks")
    args = parser.parse_args()

    # Convert paths to absolute paths
    prompt_path = Path(args.prompt).resolve()
    output_path = Path(args.output).resolve()

    # Create output directory if it doesn't exist
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # Load the prompt
    prompt = load_prompt(prompt_path)
    if not prompt:
        sys.exit(1)

    # Get initial timestamp of output file if it exists
    initial_timestamp = output_path.stat().st_mtime if output_path.exists() else None

    if args.auto:
        # Automated mode using AutoHotKey
        print("Using automated mode with AutoHotKey.")

        # Check if AutoHotKey is installed
        if not is_autohotkey_installed():
            print("AutoHotKey is not installed. Falling back to manual mode.")
            args.auto = False
        else:
            # Copy the prompt to the clipboard first
            pyperclip.copy(prompt)
            print("Prompt copied to clipboard!")

            # Print a reminder but don't ask for confirmation in auto mode
            print("\n" + "="*80)
            print("IMPORTANT: The script will now attempt to:")
            print("1. Activate Visual Studio Code")
            print("2. Open the Augment chat")
            print("3. Paste the prompt and send it")
            print("="*80 + "\n")

            # Path to the AutoHotKey script
            script_dir = Path("scripts")
            script_dir.mkdir(exist_ok=True)
            script_path = script_dir / "augment_mvcd_automation.ahk"

            # Always create/update the script with our improved version
            print(f"Creating/updating AutoHotKey script at {script_path}")
            with open(script_path, "w", encoding="utf-8") as f:
                f.write("""
; ========================================================================
; Robust Augment Chat Automation Script for AutoHotkey v2
; ========================================================================
; This script uses multiple methods to activate VSCode and Augment
; with fallbacks and detailed logging
;
; The script:
; 1. Assumes the prompt is already in the clipboard
; 2. Uses multiple methods to activate VSCode
; 3. Uses multiple methods to open Augment chat
; 4. Pastes the prompt and sends it
; 5. Provides detailed logging for troubleshooting
;
; Usage:
;   AutoHotkey.exe augment_mvcd_automation.ahk
;
; Requirements:
;   - VSCode with Augment extension installed
;
; Author: Vibe Architect Team
; ========================================================================

; Enable error handling and single instance
#Warn All, StdOut
#SingleInstance Force

; Create debug log
FileDelete("autohotkey_debug.log")
FileAppend("=== Augment Automation Debug Log ===`n`n", "autohotkey_debug.log")
FileAppend("Script started at " . FormatTime(, "yyyy-MM-dd HH:mm:ss") . "`n", "autohotkey_debug.log")

; Debug function to log active window info
LogActiveWindow() {
    activeHwnd := WinGetID("A")
    activeTitle := WinGetTitle("A")
    activeProcess := WinGetProcessName("A")
    activeClass := WinGetClass("A")

    FileAppend("Active Window Info:`n", "autohotkey_debug.log")
    FileAppend("  Title: " . activeTitle . "`n", "autohotkey_debug.log")
    FileAppend("  Process: " . activeProcess . "`n", "autohotkey_debug.log")
    FileAppend("  Class: " . activeClass . "`n", "autohotkey_debug.log")
    FileAppend("  HWND: " . activeHwnd . "`n`n", "autohotkey_debug.log")
}

; Log initial state
FileAppend("Initial window state:`n", "autohotkey_debug.log")
LogActiveWindow()

; ========================================================================
; STEP 1: Activate VSCode using multiple methods
; ========================================================================

FileAppend("Attempting to activate VSCode using multiple methods`n", "autohotkey_debug.log")

; Method 1: Try to activate by window title containing "Visual Studio Code"
FileAppend("Method 1: Activating by window title containing 'Visual Studio Code'`n", "autohotkey_debug.log")
if WinExist("ahk_exe Code.exe") {
    WinActivate
    Sleep(1000)
    FileAppend("VSCode window found by executable name`n", "autohotkey_debug.log")
} else {
    FileAppend("VSCode window NOT found by executable name`n", "autohotkey_debug.log")
}

; Method 2: Try to activate by window title containing "Visual Studio Code"
FileAppend("Method 2: Activating by window title containing 'Visual Studio Code'`n", "autohotkey_debug.log")
if WinExist("Visual Studio Code") {
    WinActivate
    Sleep(1000)
    FileAppend("VSCode window found by title`n", "autohotkey_debug.log")
} else {
    FileAppend("VSCode window NOT found by title`n", "autohotkey_debug.log")
}

; Method 3: Try to activate by window title containing "VSCode"
FileAppend("Method 3: Activating by window title containing 'VSCode'`n", "autohotkey_debug.log")
if WinExist("VSCode") {
    WinActivate
    Sleep(1000)
    FileAppend("VSCode window found by 'VSCode' in title`n", "autohotkey_debug.log")
} else {
    FileAppend("VSCode window NOT found by 'VSCode' in title`n", "autohotkey_debug.log")
}

; Log window state after activation attempts
FileAppend("Window state after VSCode activation attempts:`n", "autohotkey_debug.log")
LogActiveWindow()

; Check if we successfully activated VSCode
activeProcess := WinGetProcessName("A")
if (activeProcess = "Code.exe") {
    FileAppend("SUCCESS: VSCode is now the active window`n", "autohotkey_debug.log")
} else {
    FileAppend("WARNING: Failed to activate VSCode. Current process: " . activeProcess . "`n", "autohotkey_debug.log")
    FileAppend("Continuing anyway...`n", "autohotkey_debug.log")
}

; ========================================================================
; STEP 2: Open Augment Chat using multiple methods
; ========================================================================

FileAppend("Attempting to open Augment Chat using multiple methods`n", "autohotkey_debug.log")

; Method 1: Use Ctrl+L shortcut (primary Augment shortcut)
FileAppend("Method 1: Using Ctrl+L shortcut`n", "autohotkey_debug.log")
Send("^l")
Sleep(2000)

; Log window after first method
FileAppend("Window state after Ctrl+L:`n", "autohotkey_debug.log")
LogActiveWindow()

; Method 2: Use Ctrl+Shift+P to open command palette, then type "Augment"
FileAppend("Method 2: Using Command Palette`n", "autohotkey_debug.log")
Send("^+p")
Sleep(1000)
Send("Augment: Open Chat")
Sleep(500)
Send("{Enter}")
Sleep(2000)

; Log window after second method
FileAppend("Window state after Command Palette:`n", "autohotkey_debug.log")
LogActiveWindow()

; ========================================================================
; STEP 3: Paste the prompt and send it
; ========================================================================

FileAppend("Attempting to paste prompt and send it`n", "autohotkey_debug.log")

; Click in the chat input area (approximate position)
FileAppend("Clicking in chat input area`n", "autohotkey_debug.log")
; Try to click near the bottom of the window
WinGetPos(, , &width, &height, "A")
if (width && height) {
    ; Click near the bottom center of the window
    Click(width / 2, height - 100)
    FileAppend("Clicked at position: " . width / 2 . ", " . (height - 100) . "`n", "autohotkey_debug.log")
} else {
    FileAppend("Could not determine window dimensions`n", "autohotkey_debug.log")
}

Sleep(1000)

; Paste the prompt
FileAppend("Pasting prompt from clipboard`n", "autohotkey_debug.log")
Send("^v")
Sleep(1000)

; Send the prompt
FileAppend("Sending prompt with Enter key`n", "autohotkey_debug.log")
Send("{Enter}")
Sleep(1000)

; ========================================================================
; STEP 4: Completion
; ========================================================================

FileAppend("Automation sequence completed at " . FormatTime(, "yyyy-MM-dd HH:mm:ss") . "`n", "autohotkey_debug.log")
FileAppend("Check VSCode to see if the prompt was successfully sent to Augment.`n", "autohotkey_debug.log")

; Exit the script
ExitApp(0)
""")

            # Apply delay if specified
            if args.delay > 0:
                print(f"Waiting for {args.delay} seconds before running the script...")
                for i in range(args.delay, 0, -1):
                    print(f"{i}...", end="", flush=True)
                    time.sleep(1)
                print("\nDelay complete. Running script now.")

            # Use the batch file approach instead of direct script execution
            print("Running AutoHotkey script using batch file...")

            # Path to the batch file
            batch_path = script_dir / "run_autohotkey.bat"

            # Check if the batch file exists
            if not batch_path.exists():
                print(f"Error: Batch file not found at {batch_path}")
                print("Falling back to direct script execution...")
                automation_result = run_autohotkey_script(script_path)
            else:
                # Run the batch file
                print(f"Executing batch file: {batch_path}")
                try:
                    # Use subprocess with shell=True to handle paths with spaces
                    result = subprocess.run(f'cd "{script_dir}" && run_autohotkey.bat',
                                           shell=True,
                                           capture_output=False)

                    # Check for status file to confirm completion
                    status_file = script_dir / "autohotkey_status.txt"
                    if status_file.exists():
                        print("Found status file - automation completed")
                        automation_result = True
                        # Clean up status file
                        try:
                            os.remove(status_file)
                        except:
                            pass
                    else:
                        print("No status file found - checking return code")
                        automation_result = (result.returncode == 0)
                except Exception as e:
                    print(f"Error running batch file: {e}")
                    traceback.print_exc()
                    automation_result = False

            # Check for debug logs - try both the regular and simple log
            debug_logs = [
                script_dir / "autohotkey_debug.log",
                script_dir / "simple_augment.log"
            ]

            for debug_log in debug_logs:
                if debug_log.exists():
                    print(f"\n=== AutoHotKey Debug Log: {debug_log.name} ===")
                    try:
                        with open(debug_log, "r", encoding="utf-8") as f:
                            log_content = f.read()
                            print(log_content)
                    except Exception as e:
                        print(f"Error reading debug log: {e}")
                    print("=== End Debug Log ===\n")

            if not automation_result:
                print("\n" + "="*80)
                print("AUTOMATION FAILED - SWITCHING TO MANUAL MODE")
                print("The automatic method failed to properly interact with VSCode/Augment.")
                print("This could be due to window focus issues or shortcut conflicts.")
                print("="*80 + "\n")

                # Fall back to manual mode
                args.auto = False
            elif args.monitor:
                # Monitor the output file for changes
                print("\nAutoHotKey script ran successfully. Monitoring output file for changes...")
                monitor_file_changes(output_path, initial_timestamp)
                sys.exit(0)
            else:
                print("\nAutoHotKey script ran successfully.")
                print("Augment is now processing the prompt. Check the MVCD file for updates.")
                sys.exit(0)

    # If we're here, we're in manual mode or fallback from failed automation
    if not args.auto:
        # Manual mode
        print("\n" + "="*80)
        print("MANUAL MODE ACTIVATED")
        print("="*80)

        # Copy the prompt to the clipboard
        pyperclip.copy(prompt)
        print("✓ Prompt copied to clipboard and ready to paste!")

        # Provide detailed instructions to the user
        print("\n" + "="*80)
        print("STEP-BY-STEP INSTRUCTIONS:")
        print("="*80)
        print("1. Make sure Visual Studio Code is open and visible")
        print("2. Press Ctrl+L in VSCode to open the Augment chat panel")
        print("   (If Ctrl+L doesn't work, use View menu > Augment)")
        print("3. Click in the Augment chat input field")
        print("4. Paste the prompt (Ctrl+V) into the chat input")
        print("5. Press Enter to send the prompt")
        print("6. Wait for Augment to process and respond")
        print("7. Once Augment has finished responding, select all text (Ctrl+A)")
        print("8. Copy the entire response (Ctrl+C)")
        print("9. Return to this terminal window and press Enter to continue")
        print("="*80 + "\n")

        # Wait for user to press Enter
        input("Press Enter when you've copied the response from Augment...")

        # Save the response
        if save_response(output_path):
            print("\nSuccess! The MVCD file has been updated.")
            sys.exit(0)
        else:
            print("\nFailed to save the response. Please try again.")
            sys.exit(1)

if __name__ == "__main__":
    main()
