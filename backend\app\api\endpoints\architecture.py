from fastapi import APIRouter, HTTPException, Response
from fastapi.responses import JSONResponse
import os
import hashlib
import time
from pathlib import Path

router = APIRouter()

# Path to the .VibeArch directory
VIBEARCH_DIR = Path(os.getcwd()) / ".VibeArch"
DIAGRAMS_DIR = VIBEARCH_DIR / "Architecture" / "diagrams"

# Cache for diagram content with timestamps
diagram_cache = {}

def get_file_hash(file_path):
    """Calculate a hash of the file to detect changes"""
    if not os.path.exists(file_path):
        return None
    
    with open(file_path, "rb") as f:
        file_hash = hashlib.md5(f.read()).hexdigest()
    return file_hash

@router.get("/diagrams")
async def list_diagrams():
    """List all available architecture diagrams"""
    try:
        if not DIAGRAMS_DIR.exists():
            return JSONResponse(content={"diagrams": []})
        
        diagrams = []
        for file in DIAGRAMS_DIR.glob("*.md"):
            if file.name != "index.md":  # Skip index file
                diagram_name = file.stem
                diagrams.append({
                    "name": diagram_name,
                    "path": f"/api/architecture/diagrams/{diagram_name}",
                    "last_modified": os.path.getmtime(file)
                })
        
        return JSONResponse(content={"diagrams": diagrams})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing diagrams: {str(e)}")

@router.get("/diagrams/{diagram_name}")
async def get_diagram(diagram_name: str):
    """Get a specific architecture diagram"""
    try:
        file_path = DIAGRAMS_DIR / f"{diagram_name}.md"
        
        if not file_path.exists():
            raise HTTPException(status_code=404, detail=f"Diagram {diagram_name} not found")
        
        # Check if we have a cached version and if the file has changed
        current_hash = get_file_hash(file_path)
        last_modified = os.path.getmtime(file_path)
        
        if (diagram_name in diagram_cache and 
            diagram_cache[diagram_name]["hash"] == current_hash and
            time.time() - diagram_cache[diagram_name]["timestamp"] < 300):  # Cache for 5 minutes
            # Use cached version
            content = diagram_cache[diagram_name]["content"]
            mermaid_code = diagram_cache[diagram_name]["mermaid_code"]
        else:
            # Read the file and extract the mermaid code
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # Extract mermaid code from markdown
            import re
            mermaid_match = re.search(r"```mermaid\n([\s\S]*?)```", content)
            mermaid_code = mermaid_match.group(1) if mermaid_match else ""
            
            # Update cache
            diagram_cache[diagram_name] = {
                "content": content,
                "mermaid_code": mermaid_code,
                "hash": current_hash,
                "timestamp": time.time()
            }
        
        return JSONResponse(content={
            "name": diagram_name,
            "content": content,
            "mermaid_code": mermaid_code,
            "last_modified": last_modified
        })
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving diagram: {str(e)}")
