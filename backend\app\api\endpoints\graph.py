from fastapi import APIRouter, HTTPException, Response
from fastapi.responses import FileResponse
import os
import tempfile

from app.core.project_manager import ProjectManager
from app.services.graph_service import GraphService

router = APIRouter()
project_manager = ProjectManager()
graph_service = GraphService()

@router.get("/{project_path:path}/data")
async def get_graph_data(project_path: str):
    """Get graph data for visualization"""
    try:
        project = project_manager.get_project(project_path)
        graph_data = graph_service.generate_graph_data(project)
        return graph_data
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating graph data: {str(e)}")

@router.get("/{project_path:path}/image")
async def get_graph_image(project_path: str, format: str = "png"):
    """Generate and return a graph visualization image"""
    try:
        project = project_manager.get_project(project_path)
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{format}") as tmp:
            tmp_path = tmp.name
        
        # Generate graph image
        graph_service.generate_graph_image(project, tmp_path, format)
        
        # Return file
        return FileResponse(
            tmp_path,
            media_type=f"image/{format}",
            filename=f"{project.name}_graph.{format}"
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating graph image: {str(e)}")
