from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Any

from app.core.project_manager import ProjectManager
from app.services.llm_service import LLMService
from app.schemas.llm import PromptRequest, PromptResponse, LLMProvider

router = APIRouter()
project_manager = ProjectManager()
llm_service = LLMService()

@router.get("/providers")
async def list_providers():
    """List available LLM providers"""
    try:
        providers = llm_service.list_providers()
        return {"providers": providers}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing providers: {str(e)}")

@router.post("/prompt")
async def send_prompt(request: PromptRequest):
    """Send a prompt to the LLM"""
    try:
        # Get project if path is provided
        project = None
        if request.project_path:
            project = project_manager.get_project(request.project_path)
        
        # Send prompt to LLM
        response = llm_service.send_prompt(
            prompt=request.prompt,
            provider=request.provider,
            model=request.model,
            project=project
        )
        
        return PromptResponse(response=response)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error sending prompt: {str(e)}")
