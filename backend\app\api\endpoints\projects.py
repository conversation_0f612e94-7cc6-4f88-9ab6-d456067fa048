from typing import List, Dict, Any
import os
from fastapi import APIRouter, HTTPException, Depends, Body, Query
from pydantic import BaseModel

from app.core.project_manager import ProjectManager
from app.models.project import Project
from app.schemas.project import ProjectCreate, ProjectResponse, ProjectUpdate
from app.schemas.project_check import ProjectCheckRequest, ProjectCheckResponse, ProjectInitializeRequest
from app.core.config import settings

router = APIRouter()
project_manager = ProjectManager()

@router.get("/", response_model=List[ProjectResponse])
async def list_projects(base_directory: str):
    """List all Vibe Architect projects in the given directory"""
    try:
        projects = project_manager.list_projects(base_directory)
        return [ProjectResponse.model_validate(project) for project in projects]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing projects: {str(e)}")

@router.post("/", response_model=ProjectResponse)
async def create_project(project_data: ProjectCreate):
    """Initialize a new Vibe Architect project"""
    try:
        project = project_manager.initialize_project(
            project_path=project_data.path,
            project_name=project_data.name
        )
        return ProjectResponse.model_validate(project)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating project: {str(e)}")

@router.get("/{project_path:path}", response_model=ProjectResponse)
async def get_project(project_path: str):
    """Get project details"""
    try:
        project = project_manager.get_project(project_path)
        return ProjectResponse.model_validate(project)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving project: {str(e)}")

@router.patch("/{project_path:path}", response_model=ProjectResponse)
async def update_project(project_path: str, update_data: ProjectUpdate):
    """Update project settings"""
    try:
        project = project_manager.update_project_settings(
            project_path=project_path,
            updated_settings=update_data.model_dump(exclude_unset=True)
        )
        return ProjectResponse.model_validate(project)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating project: {str(e)}")

@router.post("/check", response_model=ProjectCheckResponse)
async def check_project(request: ProjectCheckRequest):
    """Check if a directory exists and has a .vibearch folder"""
    try:
        path = os.path.abspath(request.path)
        exists = os.path.exists(path) and os.path.isdir(path)
        has_vibearch = exists and os.path.exists(os.path.join(path, settings.VIBEARCH_DIR_NAME))

        return ProjectCheckResponse(
            exists=exists,
            hasVibeArchFolder=has_vibearch
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error checking project directory: {str(e)}")

@router.post("/initialize", response_model=ProjectResponse)
async def initialize_project(request: ProjectInitializeRequest):
    """Initialize a new Vibe Architect project in the specified directory"""
    try:
        project = project_manager.initialize_project(
            project_path=request.path,
            project_name=request.name
        )
        return ProjectResponse.model_validate(project)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error initializing project: {str(e)}")

@router.get("/load", response_model=ProjectResponse)
async def load_project(path: str = Query(...)):
    """Load a project by path"""
    try:
        project = project_manager.get_project(path)
        return ProjectResponse.model_validate(project)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error loading project: {str(e)}")
