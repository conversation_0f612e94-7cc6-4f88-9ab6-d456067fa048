from typing import List
from fastapi import APIRouter, HTTPException, Path, Body, Query

from app.core.project_manager import ProjectManager
from app.services.file_service import FileService
from app.models.task import Task, TaskStatus
from app.schemas.task import TaskCreate, TaskResponse, TaskUpdate

router = APIRouter()
project_manager = ProjectManager()
file_service = FileService()

@router.get("/{project_path:path}", response_model=List[TaskResponse])
async def list_tasks(
    project_path: str,
    status: TaskStatus = None
):
    """List tasks for a project, optionally filtered by status"""
    try:
        project = project_manager.get_project(project_path)
        tasks = project.tasks
        
        if status:
            tasks = [task for task in tasks if task.status == status]
            
        return [TaskResponse.model_validate(task) for task in tasks]
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing tasks: {str(e)}")

@router.post("/{project_path:path}", response_model=TaskResponse)
async def create_task(
    project_path: str,
    task_data: TaskCreate
):
    """Create a new task"""
    try:
        # Get current project and tasks
        project = project_manager.get_project(project_path)
        tasks = project.tasks
        
        # Create new task ID
        task_id = f"task_{len(tasks)}"
        
        # Create new task
        new_task = Task(
            id=task_id,
            title=task_data.title,
            description=task_data.description,
            status=task_data.status,
            dependencies=task_data.dependencies or []
        )
        
        # Add to tasks list
        tasks.append(new_task)
        
        # Write updated tasks
        file_service.write_tasks(project_path, tasks)
        
        return TaskResponse.model_validate(new_task)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating task: {str(e)}")

@router.patch("/{project_path:path}/{task_id}", response_model=TaskResponse)
async def update_task(
    project_path: str,
    task_id: str,
    task_data: TaskUpdate
):
    """Update a task"""
    try:
        # Get current project and tasks
        project = project_manager.get_project(project_path)
        tasks = project.tasks
        
        # Find task by ID
        task_index = next((i for i, task in enumerate(tasks) if task.id == task_id), None)
        if task_index is None:
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")
        
        # Update task
        update_data = task_data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(tasks[task_index], key, value)
        
        # Write updated tasks
        file_service.write_tasks(project_path, tasks)
        
        return TaskResponse.model_validate(tasks[task_index])
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating task: {str(e)}")

@router.delete("/{project_path:path}/{task_id}", response_model=dict)
async def delete_task(
    project_path: str,
    task_id: str
):
    """Delete a task"""
    try:
        # Get current project and tasks
        project = project_manager.get_project(project_path)
        tasks = project.tasks
        
        # Find task by ID
        task_index = next((i for i, task in enumerate(tasks) if task.id == task_id), None)
        if task_index is None:
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")
        
        # Remove task
        tasks.pop(task_index)
        
        # Write updated tasks
        file_service.write_tasks(project_path, tasks)
        
        return {"message": f"Task {task_id} deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting task: {str(e)}")
