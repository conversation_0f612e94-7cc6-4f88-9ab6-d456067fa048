from fastapi import APIRouter
from fastapi.responses import JSONResponse

from app.api.endpoints import projects, tasks, graph, llm, architecture, mvcd
from app.routers import directory

api_router = APIRouter()

@api_router.get("/health", tags=["health"])
async def health_check():
    """Health check endpoint for the API"""
    return JSONResponse(content={"status": "healthy", "message": "API is running"})

api_router.include_router(projects.router, prefix="/projects", tags=["projects"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["tasks"])
api_router.include_router(graph.router, prefix="/graph", tags=["graph"])
api_router.include_router(llm.router, prefix="/llm", tags=["llm"])
api_router.include_router(architecture.router, prefix="/architecture", tags=["architecture"])
api_router.include_router(mvcd.router, prefix="/mvcd", tags=["mvcd"])
api_router.include_router(directory.router, tags=["directory"])
