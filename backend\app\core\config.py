import os
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, field_validator
from pydantic_settings import BaseSettings
import yaml

class Settings(BaseSettings):
    # API settings
    API_V1_STR: str = "/v1"
    PROJECT_NAME: str = "Vibe Architect"

    # CORS settings
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8000", "http://localhost:5174", "http://localhost:6000", "http://localhost:5001", "http://localhost:5002"]

    # LLM settings
    DEFAULT_LLM_PROVIDER: str = "openai"
    DEFAULT_LLM_MODEL: str = "gpt-4o"

    # File paths
    VIBEARCH_DIR_NAME: str = ".vibearchitect"
    SETTINGS_FILENAME: str = "settings.yaml"
    SPEC_FILENAME: str = "spec.md"
    TASKS_FILENAME: str = "tasks.md"

    # Environment variables
    OPENAI_API_KEY: Optional[str] = None
    VITE_OPENAI_API_KEY: Optional[str] = None  # For frontend use
    GEMINI_API_KEY: Optional[str] = None  # For Gemini API

    # Allow extra fields to be flexible with environment variables
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Allow extra fields in the environment

settings = Settings()

class ProjectSettings(BaseModel):
    llm_provider: str = settings.DEFAULT_LLM_PROVIDER
    llm_model: str = settings.DEFAULT_LLM_MODEL
    api_keys: Dict[str, Optional[str]] = {}
    project_structure: Dict[str, str] = {}

    @field_validator('api_keys')
    def validate_api_keys(cls, v):
        # Ensure we have at least an empty dict
        return v or {}

    @classmethod
    def from_yaml(cls, yaml_content: str) -> 'ProjectSettings':
        """Create ProjectSettings from YAML content"""
        try:
            data = yaml.safe_load(yaml_content) or {}
            return cls(**data)
        except Exception as e:
            # If parsing fails, return default settings
            print(f"Error parsing settings YAML: {e}")
            return cls()

    def to_yaml(self) -> str:
        """Convert settings to YAML string"""
        return yaml.dump(self.model_dump(), default_flow_style=False)






























