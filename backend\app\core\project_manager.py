import os
import shutil
from typing import List, Dict, Optional, Any
from pathlib import Path

from app.core.config import settings, ProjectSettings
from app.services.file_service import FileService
from app.models.project import Project
from app.models.task import Task

class ProjectManager:
    """Manages project directories and files"""

    def __init__(self):
        self.file_service = FileService()

    def initialize_project(self, project_path: str, project_name: Optional[str] = None) -> Project:
        """Initialize a new project directory with Vibe Architect files"""
        # Create absolute path
        abs_path = os.path.abspath(project_path)

        # Create project directory if it doesn't exist
        os.makedirs(abs_path, exist_ok=True)

        # Create .vibearch directory
        vibearch_dir = os.path.join(abs_path, settings.VIBEARCH_DIR_NAME)
        os.makedirs(vibearch_dir, exist_ok=True)

        # Create standard subdirectories
        directories = [
            os.path.join(vibearch_dir, "Directory"),
            os.path.join(vibearch_dir, "Specification"),
            os.path.join(vibearch_dir, "Specification", "history"),
            os.path.join(vibearch_dir, "Architecture"),
            os.path.join(vibearch_dir, "Architecture", "diagrams"),
            os.path.join(vibearch_dir, "TechStack"),
            os.path.join(vibearch_dir, "TechStack", "config"),
            os.path.join(vibearch_dir, "Documentation"),
            os.path.join(vibearch_dir, "Documentation", "api"),
            os.path.join(vibearch_dir, "Documentation", "user")
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

        # Create default project settings
        project_settings = ProjectSettings()

        # Create default files if they don't exist
        self.file_service.write_settings(abs_path, project_settings)

        # Create specification file
        spec_path = os.path.join(vibearch_dir, "Specification", "main.md")
        if not os.path.exists(spec_path):
            with open(spec_path, 'w') as f:
                f.write(f"# {project_name or 'Project'} Specification\n\n## Overview\n\nAdd your project overview here.\n")

        # Create tasks file
        if not os.path.exists(os.path.join(vibearch_dir, settings.TASKS_FILENAME)):
            self.file_service.write_tasks(abs_path, [])

        # Create architecture overview
        arch_overview_path = os.path.join(vibearch_dir, "Architecture", "overview.md")
        if not os.path.exists(arch_overview_path):
            with open(arch_overview_path, 'w') as f:
                f.write(f"# {project_name or 'Project'} Architecture Overview\n\n## System Architecture\n\n[Provide a high-level description of the system architecture]\n\n## Components\n\n[Describe the main components of the system]\n")

        # Create architecture diagrams
        diagram_files = {
            "system_architecture.md": f"# System Architecture\n\n```mermaid\ngraph TD\n    A[{project_name or 'Project'}] --> B[Component 1]\n    A --> C[Component 2]\n    B --> D[Subcomponent 1.1]\n    B --> E[Subcomponent 1.2]\n    C --> F[Subcomponent 2.1]\n```\n",
            "frontend_architecture.md": f"# Frontend Architecture\n\n```mermaid\ngraph TD\n    A[Frontend] --> B[Component 1]\n    A --> C[Component 2]\n```\n",
            "backend_architecture.md": f"# Backend Architecture\n\n```mermaid\ngraph TD\n    A[Backend] --> B[Component 1]\n    A --> C[Component 2]\n```\n",
            "data_flow.md": f"# Data Flow\n\n```mermaid\ngraph LR\n    A[User] --> B[Frontend]\n    B --> C[API]\n    C --> D[Backend]\n    D --> E[Database]\n```\n"
        }

        for filename, content in diagram_files.items():
            file_path = os.path.join(vibearch_dir, "Architecture", "diagrams", filename)
            if not os.path.exists(file_path):
                with open(file_path, 'w') as f:
                    f.write(content)

        # Create tech stack file
        tech_stack_path = os.path.join(vibearch_dir, "TechStack", "stack.json")
        if not os.path.exists(tech_stack_path):
            with open(tech_stack_path, 'w') as f:
                f.write('{\n  "frontend": [],\n  "backend": [],\n  "database": [],\n  "devops": []\n}')

        # Create documentation readme
        docs_readme_path = os.path.join(vibearch_dir, "Documentation", "readme.md")
        if not os.path.exists(docs_readme_path):
            with open(docs_readme_path, 'w') as f:
                f.write(f"# {project_name or 'Project'} Documentation\n\n## Overview\n\nAdd your project documentation here.\n")

        # Return project info
        return self.get_project(abs_path)

    def get_project(self, project_path: str) -> Project:
        """Get project information"""
        abs_path = os.path.abspath(project_path)
        vibearch_dir = os.path.join(abs_path, settings.VIBEARCH_DIR_NAME)

        if not os.path.exists(vibearch_dir):
            raise ValueError(f"Not a Vibe Architect project: {abs_path}")

        # Read project settings
        project_settings = self.file_service.read_settings(abs_path)

        # Get project name (directory name)
        project_name = os.path.basename(abs_path)

        # Read tasks
        tasks = self.file_service.read_tasks(abs_path)

        return Project(
            name=project_name,
            path=abs_path,
            settings=project_settings,
            tasks=tasks
        )

    def list_projects(self, base_directory: str) -> List[Project]:
        """List all Vibe Architect projects in the given directory"""
        projects = []

        # Check if the base directory itself is a project
        if os.path.exists(os.path.join(base_directory, settings.VIBEARCH_DIR_NAME)):
            projects.append(self.get_project(base_directory))
            return projects

        # Check subdirectories
        for item in os.listdir(base_directory):
            item_path = os.path.join(base_directory, item)
            if os.path.isdir(item_path):
                if os.path.exists(os.path.join(item_path, settings.VIBEARCH_DIR_NAME)):
                    try:
                        projects.append(self.get_project(item_path))
                    except Exception as e:
                        print(f"Error loading project {item_path}: {e}")

        return projects

    def update_project_settings(self, project_path: str, updated_settings: Dict[str, Any]) -> Project:
        """Update project settings"""
        # Get current settings
        current_settings = self.file_service.read_settings(project_path)

        # Update settings
        for key, value in updated_settings.items():
            if hasattr(current_settings, key):
                setattr(current_settings, key, value)

        # Write updated settings
        self.file_service.write_settings(project_path, current_settings)

        # Return updated project
        return self.get_project(project_path)
