import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent.absolute()
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

from app.api.router import api_router
from app.core.config import settings
from middleware.static_files import setup_static_files

app = FastAPI(
    title="Vibe Architect API",
    description="API for managing AI-based coding agents and project structure",
    version="0.1.0",
)

# Configure CORS with more permissive settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for now to fix CORS issues
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix="/api")

# Mount static files for frontend (when built)
static_files_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")
if os.path.exists(static_files_dir):
    app.mount("/", StaticFiles(directory=static_files_dir, html=True), name="static")

# Mount .VibeArch directory for serving architecture diagrams
setup_static_files(app)

@app.get("/health")
async def health_check():
    """Health check endpoint with explicit CORS headers"""
    from fastapi.responses import JSONResponse

    # Create a response with the health status
    response = JSONResponse(content={"status": "healthy"})

    # Add explicit CORS headers to ensure the response can be accessed from any origin
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "GET, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "*"

    return response

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8080, reload=True)

