from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any
import logging
from ..services.directory_service import DirectoryService

router = APIRouter(
    prefix="/directory",
    tags=["directory"],
)

logger = logging.getLogger(__name__)

@router.get("/list")
async def list_directories(path: str = Query(..., description="Directory path to list")):
    """
    List all directories in the given path
    """
    try:
        logger.info(f"Received request to list directories at path: {path}")

        # Validate path
        if not path or len(path.strip()) == 0:
            logger.warning("Empty path provided")
            raise HTTPException(status_code=400, detail="Path cannot be empty")

        # Get directories
        directories = DirectoryService.list_directories(path)

        # Log success
        logger.info(f"Successfully listed {len(directories)} directories at {path}")

        # Add CORS headers to the response
        response = {"directories": directories}
        return response

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log the error with full traceback
        logger.error(f"Error listing directories: {str(e)}", exc_info=True)

        # Return a more user-friendly error message
        error_message = f"Error listing directories: {str(e)}"
        raise HTTPException(
            status_code=500,
            detail=error_message
        )
