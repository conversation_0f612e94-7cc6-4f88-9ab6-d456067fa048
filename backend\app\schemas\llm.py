from typing import Optional, List, Dict, Any
from pydantic import BaseModel

class LLMProvider(BaseModel):
    """Schema for LLM provider"""
    id: str
    name: str
    models: List[str]

class PromptRequest(BaseModel):
    """Schema for prompt request"""
    prompt: str
    provider: Optional[str] = None
    model: Optional[str] = None
    project_path: Optional[str] = None

class PromptResponse(BaseModel):
    """Schema for prompt response"""
    response: str
