from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

from app.core.config import ProjectSettings
from app.schemas.task import TaskResponse

class ProjectBase(BaseModel):
    """Base schema for project operations"""
    name: Optional[str] = None

class ProjectCreate(ProjectBase):
    """Schema for creating a new project"""
    path: str
    name: Optional[str] = None

class ProjectUpdate(BaseModel):
    """Schema for updating project settings"""
    llm_provider: Optional[str] = None
    llm_model: Optional[str] = None
    api_keys: Optional[Dict[str, Optional[str]]] = None
    project_structure: Optional[Dict[str, str]] = None

class ProjectResponse(ProjectBase):
    """Schema for project response"""
    name: str
    path: str
    settings: ProjectSettings
    tasks: List[TaskResponse] = []
    
    class Config:
        from_attributes = True
