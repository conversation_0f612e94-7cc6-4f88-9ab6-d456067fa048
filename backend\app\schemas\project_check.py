from pydantic import BaseModel

class ProjectCheckRequest(BaseModel):
    """Request schema for checking if a directory is a Vibe Architect project"""
    path: str

class ProjectCheckResponse(BaseModel):
    """Response schema for project check"""
    exists: bool
    hasVibeArchFolder: bool

class ProjectInitializeRequest(BaseModel):
    """Request schema for initializing a new project"""
    path: str
    name: str
