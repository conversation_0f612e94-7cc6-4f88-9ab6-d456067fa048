from typing import List, Optional
from pydantic import BaseModel

from app.models.task import TaskStatus

class TaskBase(BaseModel):
    """Base schema for task operations"""
    title: str
    description: Optional[str] = None
    status: TaskStatus = TaskStatus.TODO
    dependencies: List[str] = []

class TaskCreate(TaskBase):
    """Schema for creating a new task"""
    pass

class TaskUpdate(BaseModel):
    """Schema for updating a task"""
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    dependencies: Optional[List[str]] = None

class TaskResponse(TaskBase):
    """Schema for task response"""
    id: str
    
    class Config:
        from_attributes = True
