import os
import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class DirectoryService:
    """Service for handling directory operations"""

    @staticmethod
    def list_directories(path: str) -> List[Dict[str, Any]]:
        """
        List all directories in the given path

        Args:
            path: The path to list directories from

        Returns:
            A list of dictionaries containing directory information
        """
        try:
            # Log the request
            logger.info(f"Listing directories for path: {path}")

            # Normalize path
            path = os.path.normpath(path)
            logger.debug(f"Normalized path: {path}")

            # Check if path exists
            if not os.path.exists(path):
                logger.warning(f"Path does not exist: {path}")
                return []

            # Check if path is accessible
            if not os.access(path, os.R_OK):
                logger.warning(f"Path is not accessible: {path}")
                return []

            # Get all entries in the directory
            entries = []
            try:
                dir_entries = os.listdir(path)
                logger.debug(f"Found {len(dir_entries)} entries in {path}")

                for entry in dir_entries:
                    entry_path = os.path.join(path, entry)

                    # Include both directories and files
                    is_dir = os.path.isdir(entry_path)
                    entries.append({
                        "name": entry,
                        "path": entry_path,
                        "isDirectory": is_dir,
                        "type": "folder" if is_dir else "file"
                    })
            except PermissionError:
                logger.warning(f"Permission denied when accessing: {path}")
                return []
            except FileNotFoundError:
                logger.warning(f"Path not found: {path}")
                return []

            logger.info(f"Returning {len(entries)} directories from {path}")
            return entries

        except Exception as e:
            logger.error(f"Error listing directories: {str(e)}", exc_info=True)
            # Return empty list instead of raising to avoid breaking the API
            return []
