import os
import re
from typing import List, Dict, Any, Optional
import yaml

from app.core.config import settings, ProjectSettings
from app.models.task import Task, TaskStatus

class FileService:
    """Service for reading and writing project files"""
    
    def get_vibearch_dir(self, project_path: str) -> str:
        """Get the .vibearchitect directory path"""
        return os.path.join(project_path, settings.VIBEARCH_DIR_NAME)
    
    def read_settings(self, project_path: str) -> ProjectSettings:
        """Read settings.yaml from project directory"""
        settings_path = os.path.join(self.get_vibearch_dir(project_path), settings.SETTINGS_FILENAME)
        
        if not os.path.exists(settings_path):
            return ProjectSettings()
        
        with open(settings_path, 'r') as f:
            content = f.read()
            return ProjectSettings.from_yaml(content)
    
    def write_settings(self, project_path: str, project_settings: ProjectSettings) -> None:
        """Write settings to settings.yaml"""
        settings_path = os.path.join(self.get_vibearch_dir(project_path), settings.SETTINGS_FILENAME)
        
        with open(settings_path, 'w') as f:
            f.write(project_settings.to_yaml())
    
    def read_spec(self, project_path: str) -> str:
        """Read spec.md"""
        spec_path = os.path.join(self.get_vibearch_dir(project_path), settings.SPEC_FILENAME)
        
        if not os.path.exists(spec_path):
            return ""
        
        with open(spec_path, 'r') as f:
            return f.read()
    
    def write_spec(self, project_path: str, spec_content: str) -> None:
        """Write to spec.md"""
        spec_path = os.path.join(self.get_vibearch_dir(project_path), settings.SPEC_FILENAME)
        
        with open(spec_path, 'w') as f:
            f.write(spec_content)
    
    def read_tasks(self, project_path: str) -> List[Task]:
        """Parse tasks.md into structured task objects"""
        tasks_path = os.path.join(self.get_vibearch_dir(project_path), settings.TASKS_FILENAME)
        
        if not os.path.exists(tasks_path):
            return []
        
        with open(tasks_path, 'r') as f:
            content = f.read()
        
        # Parse tasks from markdown
        tasks = []
        
        # Find todo and done sections
        todo_section = re.search(r'## \[todo\]\s*\n(.*?)(?=\n## |$)', content, re.DOTALL)
        done_section = re.search(r'## \[done\]\s*\n(.*?)(?=\n## |$)', content, re.DOTALL)
        
        # Parse todo tasks
        if todo_section:
            todo_content = todo_section.group(1)
            todo_tasks = re.findall(r'- \[ \] (.*?)(?=\n- |\n\n|$)', todo_content, re.DOTALL)
            
            for i, task_desc in enumerate(todo_tasks):
                task_desc = task_desc.strip()
                if task_desc:
                    tasks.append(Task(
                        id=f"todo_{i}",
                        title=task_desc.split('\n')[0],
                        description='\n'.join(task_desc.split('\n')[1:]).strip(),
                        status=TaskStatus.TODO
                    ))
        
        # Parse done tasks
        if done_section:
            done_content = done_section.group(1)
            done_tasks = re.findall(r'- \[x\] (.*?)(?=\n- |\n\n|$)', done_content, re.DOTALL)
            
            for i, task_desc in enumerate(done_tasks):
                task_desc = task_desc.strip()
                if task_desc:
                    tasks.append(Task(
                        id=f"done_{i}",
                        title=task_desc.split('\n')[0],
                        description='\n'.join(task_desc.split('\n')[1:]).strip(),
                        status=TaskStatus.DONE
                    ))
        
        return tasks
    
    def write_tasks(self, project_path: str, tasks: List[Task]) -> None:
        """Write tasks to tasks.md in proper format"""
        tasks_path = os.path.join(self.get_vibearch_dir(project_path), settings.TASKS_FILENAME)
        
        # Separate tasks by status
        todo_tasks = [task for task in tasks if task.status == TaskStatus.TODO]
        done_tasks = [task for task in tasks if task.status == TaskStatus.DONE]
        
        # Format markdown content
        content = "## [todo]\n\n"
        
        for task in todo_tasks:
            content += f"- [ ] {task.title}\n"
            if task.description:
                content += f"  {task.description.replace(chr(10), chr(10) + '  ')}\n"
            content += "\n"
        
        content += "## [done]\n\n"
        
        for task in done_tasks:
            content += f"- [x] {task.title}\n"
            if task.description:
                content += f"  {task.description.replace(chr(10), chr(10) + '  ')}\n"
            content += "\n"
        
        # Write to file
        with open(tasks_path, 'w') as f:
            f.write(content)
