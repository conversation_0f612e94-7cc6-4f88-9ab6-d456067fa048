import os
import networkx as nx
import matplotlib.pyplot as plt
from typing import Dict, List, Any

from app.models.project import Project
from app.models.task import Task, TaskStatus

class GraphService:
    """Service for generating graph visualizations"""
    
    def generate_graph_data(self, project: Project) -> Dict[str, Any]:
        """Generate graph data for visualization"""
        G = nx.DiGraph()
        
        # Add project as central node
        G.add_node(project.name, type="project")
        
        # Add tasks as nodes
        for task in project.tasks:
            G.add_node(task.id, 
                       type="task", 
                       title=task.title, 
                       status=task.status)
            
            # Connect task to project
            G.add_edge(project.name, task.id)
            
            # Add dependencies
            for dep_id in task.dependencies:
                if any(t.id == dep_id for t in project.tasks):
                    G.add_edge(dep_id, task.id)
        
        # Convert to JSON-serializable format
        nodes = []
        for node, attrs in G.nodes(data=True):
            node_data = {
                "id": node,
                **attrs
            }
            nodes.append(node_data)
        
        edges = []
        for source, target in G.edges():
            edges.append({
                "source": source,
                "target": target
            })
        
        return {
            "nodes": nodes,
            "edges": edges
        }
    
    def generate_graph_image(self, project: Project, output_path: str, format: str = "png") -> None:
        """Generate a graph visualization image"""
        G = nx.DiGraph()
        
        # Add project as central node
        G.add_node(project.name, type="project")
        
        # Add tasks as nodes
        for task in project.tasks:
            G.add_node(task.id, 
                       type="task", 
                       title=task.title, 
                       status=task.status)
            
            # Connect task to project
            G.add_edge(project.name, task.id)
            
            # Add dependencies
            for dep_id in task.dependencies:
                if any(t.id == dep_id for t in project.tasks):
                    G.add_edge(dep_id, task.id)
        
        # Create node colors based on type and status
        node_colors = []
        for node in G.nodes():
            if G.nodes[node].get('type') == 'project':
                node_colors.append('lightblue')
            elif G.nodes[node].get('status') == TaskStatus.DONE:
                node_colors.append('lightgreen')
            else:
                node_colors.append('lightcoral')
        
        # Create labels
        labels = {}
        for node in G.nodes():
            if G.nodes[node].get('type') == 'project':
                labels[node] = project.name
            else:
                task_title = G.nodes[node].get('title', node)
                # Truncate long titles
                if len(task_title) > 20:
                    task_title = task_title[:17] + "..."
                labels[node] = task_title
        
        # Create figure
        plt.figure(figsize=(12, 8))
        
        # Draw graph
        pos = nx.spring_layout(G, seed=42)
        nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=500)
        nx.draw_networkx_edges(G, pos, arrows=True)
        nx.draw_networkx_labels(G, pos, labels=labels, font_size=10)
        
        # Save figure
        plt.axis('off')
        plt.tight_layout()
        plt.savefig(output_path, format=format, dpi=300)
        plt.close()
