from typing import Dict, List, Any, Optional
import os

from app.core.config import settings
from app.models.project import Project

class LLMService:
    """Service for interacting with LLMs"""
    
    def list_providers(self) -> List[Dict[str, Any]]:
        """List available LLM providers"""
        # For now, just return a static list
        # In a real implementation, this could check for API keys and available models
        return [
            {
                "id": "openai",
                "name": "OpenAI",
                "models": ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"]
            },
            {
                "id": "anthropic",
                "name": "Anthropic",
                "models": ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"]
            }
        ]
    
    def send_prompt(
        self, 
        prompt: str, 
        provider: str = None, 
        model: str = None,
        project: Optional[Project] = None
    ) -> str:
        """Send a prompt to the LLM"""
        # Use project settings if available
        if project:
            provider = provider or project.settings.llm_provider
            model = model or project.settings.llm_model
        else:
            provider = provider or settings.DEFAULT_LLM_PROVIDER
            model = model or settings.DEFAULT_LLM_MODEL
        
        # Check for API key
        api_key = None
        if provider == "openai":
            api_key = os.environ.get("OPENAI_API_KEY")
            if not api_key and project:
                api_key = project.settings.api_keys.get("openai")
        elif provider == "anthropic":
            api_key = os.environ.get("ANTHROPIC_API_KEY")
            if not api_key and project:
                api_key = project.settings.api_keys.get("anthropic")
        
        if not api_key:
            # For now, return a placeholder response
            # In a real implementation, this would raise an error
            return f"[No API key found for {provider}. Please configure your API key in settings.]"
        
        # In a real implementation, this would call the appropriate LLM API
        # For now, return a placeholder response
        return f"[This is a placeholder response. In a real implementation, this would call the {provider} API with model {model}.]"
