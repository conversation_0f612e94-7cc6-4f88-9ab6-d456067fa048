import os
import re
import yaml
import fnmatch
from pathlib import Path
from typing import List, Dict, Any, Optional, Set


class MVCDService:
    """
    Service for scanning the codebase and generating a YAML file that describes
    the project's code elements (components, hooks, services, utilities).

    The MVCDService is responsible for:
    1. Walking the codebase to find relevant files
    2. Parsing files to extract metadata (element types, dependencies, LOC)
    3. Generating placeholder descriptions for new entries
    4. Preserving existing descriptions and confidence scores when re-scanning
    5. Outputting a structured YAML file for further enrichment by LLM agents

    The generated YAML file serves as a comprehensive map of the codebase that
    can be progressively enriched through iterative analysis.
    """

    def __init__(self, project_root: Optional[str] = None):
        """
        Initialize the MVCDService.

        Args:
            project_root: The root directory of the project. If None, uses the current directory.
        """
        self.project_root = Path(project_root or os.getcwd()).resolve()
        self.ignore_patterns = []
        self.vibearch_dir = self.project_root / ".VibeArch"
        self.ignore_file_path = self.vibearch_dir / "setup" / ".mvcd-ignore.yaml"
        self.output_path = self.vibearch_dir / "Directory" / "mvcd.yaml"

        # Load ignore patterns
        self.ignore_patterns = self.load_ignore_file()

    def load_ignore_file(self) -> List[str]:
        """
        Load the .mvcd-ignore.yaml file.

        Returns:
            A list of ignore patterns.
        """
        # Check if the ignore file exists in the VibeArch_Setup directory
        vibearch_setup_ignore_path = self.vibearch_dir / "VibeArch_Setup" / ".mvcd-ignore.yaml"

        # Try both potential locations
        for ignore_path in [self.ignore_file_path, vibearch_setup_ignore_path]:
            if ignore_path.exists():
                try:
                    with open(ignore_path, 'r') as f:
                        data = yaml.safe_load(f)
                        if data and 'ignore' in data and isinstance(data['ignore'], list):
                            return data['ignore']
                except Exception as e:
                    print(f"Error loading ignore file: {e}")

        # Default ignore patterns if file doesn't exist or is invalid
        return [
            ".git/",
            ".venv/",
            ".VibeArch/setup/",  # Only ignore the setup folder within .VibeArch
            ".VibeArch/VibeArch_Setup/",  # Only ignore the VibeArch_Setup folder within .VibeArch
            "node_modules/",
            "__pycache__/",
            "**/*.test.*",
            "**/*.spec.*",
            "**/*.stories.*",
            "**/__mocks__/**",
            "**/*.md",
            "**/*.svg",
            "**/*.jpg",
            "**/*.png",
            "**/*.ico",
            "**/*.css",
            "**/*.scss",
            "**/*.json",
            "**/*.lock",
            "**/*.config.js",
            "**/*.config.ts",
        ]

    def should_ignore(self, path: Path) -> bool:
        """
        Check if a file or directory path matches any ignore rule.

        Args:
            path: The path to check.

        Returns:
            True if the path should be ignored, False otherwise.
        """
        # Explicitly ignore .VibeArch directory
        if ".VibeArch" in str(path):
            return True

        # Convert path to a relative path from project root
        rel_path = str(path.relative_to(self.project_root))

        # Replace backslashes with forward slashes for consistent pattern matching
        rel_path = rel_path.replace("\\", "/")

        # Check if the path matches any ignore pattern
        for pattern in self.ignore_patterns:
            # Handle directory patterns (ending with /)
            if pattern.endswith('/'):
                if fnmatch.fnmatch(rel_path + '/', pattern) or fnmatch.fnmatch(rel_path, pattern):
                    return True
            # Handle file patterns
            elif fnmatch.fnmatch(rel_path, pattern):
                return True

        return False

    def load_existing_mvcd(self) -> List[Dict[str, Any]]:
        """
        Load existing MVCD data if available.

        Returns:
            A list of existing MVCD entries.
        """
        if self.output_path.exists():
            try:
                with open(self.output_path, 'r') as f:
                    data = yaml.safe_load(f)
                    if data and 'codebase' in data and isinstance(data['codebase'], list):
                        return data['codebase']
            except Exception as e:
                print(f"Error loading existing MVCD file: {e}")

        return []

    def count_lines_of_code(self, file_path: Path) -> int:
        """
        Count the number of non-blank, non-comment lines in a file.

        Args:
            file_path: Path to the file.

        Returns:
            Number of lines of code.
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Remove comments based on file extension
            ext = file_path.suffix.lower()

            if ext in ['.py']:
                # Remove Python comments and docstrings
                content = re.sub(r'"""[\s\S]*?"""', '', content)
                content = re.sub(r"'''[\s\S]*?'''", '', content)
                content = re.sub(r'#.*$', '', content, flags=re.MULTILINE)
            elif ext in ['.js', '.jsx', '.ts', '.tsx']:
                # Remove JS/TS comments
                content = re.sub(r'//.*$', '', content, flags=re.MULTILINE)
                content = re.sub(r'/\*[\s\S]*?\*/', '', content)

            # Count non-blank lines
            lines = [line.strip() for line in content.split('\n')]
            return sum(1 for line in lines if line)

        except Exception as e:
            print(f"Error counting lines in {file_path}: {e}")
            return 0

    def detect_dependencies(self, file_path: Path) -> List[str]:
        """
        Detect external dependencies imported in a file.

        Args:
            file_path: Path to the file.

        Returns:
            List of external dependencies.
        """
        dependencies = set()

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            ext = file_path.suffix.lower()

            if ext == '.py':
                # Match Python imports
                import_patterns = [
                    r'^\s*import\s+([a-zA-Z0-9_]+)',  # import module
                    r'^\s*from\s+([a-zA-Z0-9_]+)\s+import',  # from module import
                ]

                for pattern in import_patterns:
                    for match in re.finditer(pattern, content, re.MULTILINE):
                        module = match.group(1)
                        # Skip relative imports and standard library
                        if not module.startswith('.') and module not in ['os', 'sys', 're', 'pathlib', 'typing', 'json', 'datetime', 'time', 'math', 'random', 'collections', 'itertools', 'functools', 'abc', 'copy', 'enum']:
                            dependencies.add(module)

            elif ext in ['.js', '.jsx', '.ts', '.tsx']:
                # Match JS/TS imports
                import_patterns = [
                    r'^\s*import\s+.*\s+from\s+[\'"]([^\.][^\'"/]+)[\'"]',  # import x from 'module'
                    r'^\s*import\s+[\'"]([^\.][^\'"/]+)[\'"]',  # import 'module'
                    r'^\s*const\s+.*\s+=\s+require\([\'"]([^\.][^\'"/]+)[\'"]\)',  # const x = require('module')
                ]

                for pattern in import_patterns:
                    for match in re.finditer(pattern, content, re.MULTILINE):
                        module = match.group(1)
                        dependencies.add(module)

        except Exception as e:
            print(f"Error detecting dependencies in {file_path}: {e}")

        return sorted(list(dependencies))

    def detect_element_type(self, file_path: Path, content: str) -> str:
        """
        Detect the type of the main element in a file.

        Args:
            file_path: Path to the file.
            content: File content.

        Returns:
            Element type (Component, Hook, Utility, Store, Context, Type, Other).
        """
        ext = file_path.suffix.lower()
        filename = file_path.name.lower()

        # React component detection
        if ext in ['.jsx', '.tsx']:
            if re.search(r'function\s+[A-Z][a-zA-Z0-9]*\s*\(', content) or re.search(r'const\s+[A-Z][a-zA-Z0-9]*\s*=', content):
                return "Component"

        # React hook detection
        if 'hook' in filename or re.search(r'function\s+use[A-Z]', content):
            return "Hook"

        # Context detection
        if 'context' in filename or re.search(r'createContext', content):
            return "Context"

        # Store detection
        if 'store' in filename or 'reducer' in filename or re.search(r'createStore|configureStore|createSlice', content):
            return "Store"

        # Type detection
        if ext == '.ts' or ext == '.d.ts' or re.search(r'interface\s+|type\s+[A-Z][a-zA-Z0-9]*\s*=', content):
            return "Type"

        # Service/Utility detection
        if 'service' in filename or 'util' in filename:
            return "Utility"

        # Default
        return "Other"

    def detect_main_element(self, file_path: Path, content: str) -> str:
        """
        Detect the name of the main element in a file.

        Args:
            file_path: Path to the file.
            content: File content.

        Returns:
            Name of the main element.
        """
        filename = file_path.stem
        ext = file_path.suffix.lower()

        # Try to find exported components, functions, or classes
        if ext in ['.js', '.jsx', '.ts', '.tsx']:
            # Look for React components
            component_match = re.search(r'export\s+(?:default\s+)?(?:function|const)\s+([A-Z][a-zA-Z0-9]*)', content)
            if component_match:
                return component_match.group(1)

            # Look for default exports
            default_export = re.search(r'export\s+default\s+([A-Za-z][a-zA-Z0-9]*)', content)
            if default_export:
                return default_export.group(1)

        elif ext == '.py':
            # Look for Python classes
            class_match = re.search(r'class\s+([A-Z][a-zA-Z0-9]*)', content)
            if class_match:
                return class_match.group(1)

            # Look for main functions
            func_match = re.search(r'def\s+([a-z][a-zA-Z0-9_]*)', content)
            if func_match:
                return func_match.group(1)

        # Default to filename if no element found
        return filename

    def scan_codebase(self) -> List[Dict[str, Any]]:
        """
        Recursively walk the codebase and generate MVCD entries.

        Returns:
            A list of MVCD entries.
        """
        entries = []

        for root, dirs, files in os.walk(self.project_root):
            root_path = Path(root)

            # Skip directories that match ignore patterns
            dirs[:] = [d for d in dirs if not self.should_ignore(root_path / d)]

            for file in files:
                file_path = root_path / file

                # Skip files that match ignore patterns
                if self.should_ignore(file_path):
                    continue

                # Skip non-code files based on extension
                ext = file_path.suffix.lower()
                if ext not in ['.py', '.js', '.jsx', '.ts', '.tsx', '.vue', '.rb', '.go', '.java', '.php', '.cs']:
                    continue

                try:
                    # Read file content
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    # Get relative path from project root
                    rel_path = str(file_path.relative_to(self.project_root)).replace("\\", "/")

                    # Detect main element and type
                    element = self.detect_main_element(file_path, content)
                    element_type = self.detect_element_type(file_path, content)

                    # Count lines of code
                    loc = self.count_lines_of_code(file_path)

                    # Detect dependencies
                    dependencies = self.detect_dependencies(file_path)

                    # Get file modification time
                    try:
                        last_modified = os.path.getmtime(file_path)
                    except Exception:
                        last_modified = None

                    # Create entry
                    entry = {
                        'file': rel_path,
                        'element': element,
                        'type': element_type,
                        'description': 'TODO: Add description',
                        'confidence': 0,  # Default confidence score
                        'status': 'active',  # Default status
                        'dependencies': dependencies,
                        'loc': loc,
                        'last_modified': last_modified  # Add last modified timestamp
                    }

                    entries.append(entry)

                except Exception as e:
                    print(f"Error processing {file_path}: {e}")

        return entries

    def merge_with_existing(self, existing: List[Dict[str, Any]], new: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Merge new entries with existing entries, preserving descriptions.

        Args:
            existing: List of existing entries.
            new: List of new entries.

        Returns:
            Merged list of entries.
        """
        # Create a dictionary of existing entries for quick lookup
        existing_dict = {}
        for entry in existing:
            key = f"{entry.get('file')}:{entry.get('element')}"
            existing_dict[key] = entry

        # Merge new entries with existing ones
        merged = []
        processed_keys = set()

        for entry in new:
            key = f"{entry.get('file')}:{entry.get('element')}"
            processed_keys.add(key)

            if key in existing_dict:
                # Preserve description and confidence from existing entry
                existing_entry = existing_dict[key]

                # Preserve description if it's not the default
                if existing_entry.get('description') != 'TODO: Add description':
                    entry['description'] = existing_entry.get('description')

                # Preserve confidence score if it exists
                if 'confidence' in existing_entry:
                    entry['confidence'] = existing_entry.get('confidence')
                elif entry.get('description') != 'TODO: Add description':
                    # If entry has a description but no confidence, assign a default of 70%
                    entry['confidence'] = 70

                # Preserve status if it exists
                if 'status' in existing_entry:
                    entry['status'] = existing_entry.get('status')
                else:
                    # If no status exists, default to 'active'
                    entry['status'] = 'active'

                # Preserve last_modified if it exists in the existing entry
                if 'last_modified' in existing_entry:
                    # Only keep the existing timestamp if it's newer than the current one
                    if entry.get('last_modified') is None or (
                        existing_entry.get('last_modified') is not None and
                        existing_entry.get('last_modified') > entry.get('last_modified')
                    ):
                        entry['last_modified'] = existing_entry.get('last_modified')

                # Update the entry with new information
                merged.append(entry)
            else:
                # Add new entry
                merged.append(entry)

        # Add existing entries that weren't in the new list
        for key, entry in existing_dict.items():
            if key not in processed_keys:
                merged.append(entry)

        return merged

    def save_mvcd(self, entries: List[Dict[str, Any]]) -> None:
        """
        Save MVCD entries to the output file.

        Args:
            entries: List of MVCD entries.
        """
        # Create output directory if it doesn't exist
        self.output_path.parent.mkdir(parents=True, exist_ok=True)

        # Sort entries by file path for consistency
        sorted_entries = sorted(entries, key=lambda x: x.get('file', ''))

        # Create output data
        output_data = {
            'codebase': sorted_entries
        }

        # Write to file with proper formatting
        with open(self.output_path, 'w', encoding='utf-8') as f:
            yaml.dump(output_data, f, default_flow_style=False, sort_keys=False)

        print(f"MVCD file saved to {self.output_path}")

    def generate(self) -> None:
        """
        Generate the MVCD file by scanning the codebase and merging with existing data.
        """
        # Load existing MVCD data
        existing_entries = self.load_existing_mvcd()

        # Scan codebase for new entries
        new_entries = self.scan_codebase()

        # Merge new entries with existing ones
        merged_entries = self.merge_with_existing(existing_entries, new_entries)

        # Save merged entries
        self.save_mvcd(merged_entries)
