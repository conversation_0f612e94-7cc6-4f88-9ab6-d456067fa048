"""
Static file middleware for serving files from .VibeArch directories.
"""

import os
from pathlib import Path
from fastapi import FastAP<PERSON>, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from starlette.exceptions import HTTPException

class VibeArchStaticFiles(StaticFiles):
    """Custom StaticFiles class that can serve files from different project directories"""

    async def get_response(self, path: str, scope):
        """Override to handle project-specific paths"""
        request = Request(scope)
        project_path = request.query_params.get("project")

        if project_path and os.path.exists(project_path) and os.path.isdir(project_path):
            # If a project path is specified, look for the file in that project's .VibeArch directory
            vibe_arch_dir = os.path.join(project_path, ".VibeArch")
            if os.path.exists(vibe_arch_dir):
                file_path = os.path.join(vibe_arch_dir, path)
                if os.path.exists(file_path) and os.path.isfile(file_path):
                    return FileResponse(file_path)

        # Fall back to the default behavior (looking in the app's .VibeArch directory)
        try:
            return await super().get_response(path, scope)
        except HTTPException as e:
            if e.status_code == 404:
                # If file not found, return a more helpful error
                return JSONResponse(
                    status_code=404,
                    content={"detail": f"File not found: {path}"}
                )
            raise

def setup_static_files(app: FastAPI):
    """
    Set up static file serving for .VibeArch directories.

    Args:
        app: The FastAPI application instance
    """
    try:
        # Get the current working directory
        cwd = os.getcwd()

        # Define the path to the .VibeArch directory
        vibe_arch_dir = os.path.join(cwd, ".VibeArch")

        # Check if the directory exists
        if os.path.exists(vibe_arch_dir) and os.path.isdir(vibe_arch_dir):
            # Mount the .VibeArch directory to be served at /.VibeArch
            app.mount("/.VibeArch", VibeArchStaticFiles(directory=vibe_arch_dir), name="vibe_arch_files")
            print(f"Mounted .VibeArch directory at /.VibeArch")
        else:
            # Create an empty directory to mount
            os.makedirs(vibe_arch_dir, exist_ok=True)
            app.mount("/.VibeArch", VibeArchStaticFiles(directory=vibe_arch_dir), name="vibe_arch_files")
            print(f"Created and mounted .VibeArch directory at /.VibeArch")
    except Exception as e:
        print(f"Error setting up static files: {e}")
        # Continue without mounting static files
