from setuptools import setup, find_packages

setup(
    name="vibe-architect-backend",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "fastapi>=0.95.0",
        "uvicorn>=0.21.1",
        "pydantic>=2.0.0",
        "python-dotenv>=1.0.0",
        "pyyaml>=6.0",
        "networkx==3.1",
        "matplotlib==3.7.2",
        "numpy==1.24.3",
        "pytest>=7.3.1",
        "httpx>=0.24.0",
        "python-multipart>=0.0.6",
    ],
)
