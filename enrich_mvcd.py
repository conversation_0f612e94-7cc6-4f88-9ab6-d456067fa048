"""
MVCD Enrichment Script

This script runs the MVCD enrichment process to automatically generate and enrich
the Minimum Viable Code Description (MVCD) file using a coding agent.

Usage:
    python enrich_mvcd.py [--url URL] [--headless] [--timeout SECONDS]

Options:
    --url URL       URL of the coding agent (default: https://chat.openai.com/)
    --headless      Run browser in headless mode
    --timeout       Timeout in seconds (default: 300)

Environment Variables:
    CODING_AGENT_URL: URL of the coding agent
    HEADLESS: Run browser in headless mode (true/false)
    TIMEOUT: Timeout in seconds
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.abspath("."))

# Import the MVCDEnrichmentService
from backend.app.services.mvcd_enrichment import main

if __name__ == "__main__":
    # Run the enrichment process
    main()
