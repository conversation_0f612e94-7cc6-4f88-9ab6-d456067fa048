<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vibe Architect API Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        h1, h2, h3 {
            color: #2563eb;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .endpoint-list {
            list-style: none;
            padding: 0;
        }
        .endpoint-item {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .endpoint-item:hover {
            background-color: #f3f4f6;
            border-color: #d1d5db;
        }
        .method {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 8px;
        }
        .method-get {
            background-color: #dbeafe;
            color: #1e40af;
        }
        .method-post {
            background-color: #dcfce7;
            color: #166534;
        }
        .method-put, .method-patch {
            background-color: #fef3c7;
            color: #92400e;
        }
        .method-delete {
            background-color: #fee2e2;
            color: #b91c1c;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            overflow-x: auto;
        }
        .success {
            color: #16a34a;
        }
        .error {
            color: #dc2626;
        }
        pre {
            background-color: #1e293b;
            color: #e2e8f0;
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
        }
        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        input, select {
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin-right: 8px;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 12px;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #2563eb;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <a href="/" class="back-link">← Back to Home</a>
    
    <h1>Vibe Architect API Test</h1>
    <div class="card">
        <h2>API Connection Status</h2>
        <div id="connection-status">Checking connection...</div>
        
        <div class="form-group" style="margin-top: 15px;">
            <label for="baseUrl">Base API URL:</label>
            <input type="text" id="baseUrl" value="http://localhost:8080" style="width: 250px;">
            <button onclick="checkConnection()">Test Connection</button>
        </div>
    </div>
    
    <div class="card">
        <h2>Common Endpoints</h2>
        <ul class="endpoint-list">
            <li class="endpoint-item" onclick="testEndpoint('GET', '/health')">
                <span class="method method-get">GET</span> /health
                <span style="color: #6b7280; font-size: 14px;">- Health check endpoint</span>
            </li>
            <li class="endpoint-item" onclick="testEndpoint('GET', '/api/projects', {base_directory: 'C:/Users/<USER>/Documents/VSCode/Vibearch'})">
                <span class="method method-get">GET</span> /api/projects
                <span style="color: #6b7280; font-size: 14px;">- List projects</span>
            </li>
            <li class="endpoint-item" onclick="testEndpoint('GET', '/api/llm/providers')">
                <span class="method method-get">GET</span> /api/llm/providers
                <span style="color: #6b7280; font-size: 14px;">- List LLM providers</span>
            </li>
        </ul>
    </div>
    
    <div class="card">
        <h2>Custom Request</h2>
        <div class="form-group">
            <label for="method">Method:</label>
            <select id="method">
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="PATCH">PATCH</option>
                <option value="DELETE">DELETE</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="endpoint">Endpoint:</label>
            <input type="text" id="endpoint" value="/health" style="width: 250px;">
        </div>
        
        <div class="form-group">
            <label for="params">Query Parameters (JSON):</label>
            <input type="text" id="params" value="{}" style="width: 250px;">
        </div>
        
        <div class="form-group">
            <label for="body">Request Body (JSON):</label>
            <input type="text" id="body" value="{}" style="width: 250px;">
        </div>
        
        <button onclick="sendCustomRequest()">Send Request</button>
    </div>
    
    <div id="result" class="result">
        <p>Results will appear here</p>
    </div>

    <script>
        // Check API connection on page load
        window.onload = function() {
            checkConnection();
        };
        
        async function checkConnection() {
            const baseUrl = document.getElementById('baseUrl').value;
            const statusDiv = document.getElementById('connection-status');
            const healthUrl = `${baseUrl}/health`;
            
            statusDiv.innerHTML = `<p>Testing connection to: ${healthUrl}</p>`;
            
            try {
                console.log('Fetching:', healthUrl);
                const response = await fetch(healthUrl);
                const data = await response.json();
                
                console.log('Response:', data);
                
                if (data.status === 'healthy') {
                    statusDiv.innerHTML = `<p class="success">✅ API is healthy and responding correctly!</p>`;
                } else {
                    statusDiv.innerHTML = `<p class="warning">⚠️ API responded but health status is unexpected: ${JSON.stringify(data)}</p>`;
                }
            } catch (error) {
                console.error('Error:', error);
                statusDiv.innerHTML = `<p class="error">❌ Connection failed: ${error.message}</p>`;
            }
        }
        
        async function testEndpoint(method, endpoint, queryParams = {}) {
            const baseUrl = document.getElementById('baseUrl').value;
            const resultDiv = document.getElementById('result');
            
            // Build URL with query parameters
            let url = `${baseUrl}${endpoint}`;
            if (Object.keys(queryParams).length > 0) {
                const params = new URLSearchParams();
                for (const [key, value] of Object.entries(queryParams)) {
                    params.append(key, value);
                }
                url += `?${params.toString()}`;
            }
            
            resultDiv.innerHTML = `<p>Testing: ${method} ${url}</p>`;
            
            try {
                const response = await fetch(url, { method });
                const contentType = response.headers.get('content-type');
                
                if (contentType && contentType.includes('application/json')) {
                    const data = await response.json();
                    resultDiv.innerHTML += `<p class="success">Status: ${response.status} ${response.statusText}</p>`;
                    resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    const text = await response.text();
                    resultDiv.innerHTML += `<p class="success">Status: ${response.status} ${response.statusText}</p>`;
                    resultDiv.innerHTML += `<pre>${text}</pre>`;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML += `<p class="error">Error: ${error.message}</p>`;
            }
        }
        
        async function sendCustomRequest() {
            const method = document.getElementById('method').value;
            const endpoint = document.getElementById('endpoint').value;
            const paramsStr = document.getElementById('params').value;
            const bodyStr = document.getElementById('body').value;
            
            let queryParams = {};
            try {
                queryParams = JSON.parse(paramsStr);
            } catch (e) {
                alert('Invalid JSON in query parameters');
                return;
            }
            
            let body = {};
            try {
                body = JSON.parse(bodyStr);
            } catch (e) {
                alert('Invalid JSON in request body');
                return;
            }
            
            if (method === 'GET' || method === 'DELETE') {
                testEndpoint(method, endpoint, queryParams);
            } else {
                const baseUrl = document.getElementById('baseUrl').value;
                const resultDiv = document.getElementById('result');
                
                // Build URL with query parameters
                let url = `${baseUrl}${endpoint}`;
                if (Object.keys(queryParams).length > 0) {
                    const params = new URLSearchParams();
                    for (const [key, value] of Object.entries(queryParams)) {
                        params.append(key, value);
                    }
                    url += `?${params.toString()}`;
                }
                
                resultDiv.innerHTML = `<p>Testing: ${method} ${url}</p>`;
                
                try {
                    const response = await fetch(url, {
                        method,
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(body)
                    });
                    
                    const contentType = response.headers.get('content-type');
                    
                    if (contentType && contentType.includes('application/json')) {
                        const data = await response.json();
                        resultDiv.innerHTML += `<p class="success">Status: ${response.status} ${response.statusText}</p>`;
                        resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                    } else {
                        const text = await response.text();
                        resultDiv.innerHTML += `<p class="success">Status: ${response.status} ${response.statusText}</p>`;
                        resultDiv.innerHTML += `<pre>${text}</pre>`;
                    }
                } catch (error) {
                    console.error('Error:', error);
                    resultDiv.innerHTML += `<p class="error">Error: ${error.message}</p>`;
                }
            }
        }
    </script>
</body>
</html>
