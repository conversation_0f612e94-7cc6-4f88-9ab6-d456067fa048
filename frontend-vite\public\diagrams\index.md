# Architecture Diagrams

This directory contains architecture diagrams for the Vibe Architect application. These diagrams are designed to be displayed in the Vibe Architect browser UI under the Architecture tab.

## Available Diagrams

1. [System Architecture](system_architecture.md)
   - High-level overview of the entire system
   - Shows the relationships between frontend, API, backend, and storage
   - Provides a comprehensive view of all components

2. [Frontend Architecture](frontend_architecture.md)
   - Detailed view of the frontend components
   - Shows the relationships between pages, components, services, and hooks
   - Includes information about state management and styling

3. [Backend Architecture](backend_architecture.md)
   - Detailed view of the API and backend services
   - Shows the relationships between endpoints, services, models, and storage
   - Includes information about data flow and middleware

4. [Data Flow](data_flow.md)
   - Shows how data moves through the system
   - Includes examples of common user interactions
   - Demonstrates the request-response cycle

## How to View These Diagrams

These diagrams are written in Mermaid markdown format, which can be rendered in the Vibe Architect UI. To view them:

1. Open the Vibe Architect application
2. Navigate to the Architecture tab
3. Select a diagram from the list
4. The diagram will be rendered in the main content area

## How to Update These Diagrams

To update these diagrams:

1. Edit the corresponding markdown file
2. Update the Mermaid syntax as needed
3. Save the file
4. Refresh the Vibe Architect UI to see the changes

## Mermaid Syntax

These diagrams use Mermaid syntax. For more information about Mermaid, see the [Mermaid documentation](https://mermaid-js.github.io/mermaid/#/).
