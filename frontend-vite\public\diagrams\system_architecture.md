# System Architecture

```mermaid
graph TD
    User[User] -->|interacts with| Frontend

    subgraph "Frontend (Vite + React)"
        Frontend[Frontend Application] --> NavBar[Navigation Bar]
        Frontend --> Sidebar[Sidebar]
        Frontend --> MainContent[Main Content Area]

        MainContent --> Dashboard[Dashboard]
        MainContent --> Projects[Projects Management]
        MainContent --> Tasks[Tasks Management]
        MainContent --> LLM[LLM Interface]
        MainContent --> Visualization[Visualization]
        MainContent --> Settings[Settings]

        Dashboard --> ProjectStats[Project Statistics]
        Dashboard --> RecentProjects[Recent Projects]

        Projects --> ProjectList[Project List]
        Projects --> ProjectDetails[Project Details]
        Projects --> ProjectCreation[Project Creation]

        Tasks --> TaskList[Task List]
        Tasks --> TaskDetails[Task Details]
        Tasks --> TaskCreation[Task Creation]

        LLM --> PromptInput[Prompt Input]
        LLM --> ModelSelection[Model Selection]
        LLM --> ResponseDisplay[Response Display]

        Visualization --> GraphView[Graph View]
        Visualization --> ExportOptions[Export Options]

        Settings --> APIKeys[API Keys]
        Settings --> Preferences[User Preferences]
    end

    Frontend -->|HTTP Requests| API

    subgraph "API Layer (FastAPI)"
        API[API Gateway] --> ProjectsAPI[Projects API]
        API --> TasksAPI[Tasks API]
        API --> LLMAPI[LLM API]
        API --> VisualizationAPI[Visualization API]
        API --> SettingsAPI[Settings API]

        ProjectsAPI --> ProjectsCRUD[Projects CRUD]
        ProjectsAPI --> ProjectScanning[Project Scanning]

        TasksAPI --> TasksCRUD[Tasks CRUD]
        TasksAPI --> TaskDependencies[Task Dependencies]

        LLMAPI --> TextGeneration[Text Generation]
        LLMAPI --> ModelManagement[Model Management]

        VisualizationAPI --> GraphGeneration[Graph Generation]
        VisualizationAPI --> GraphExport[Graph Export]

        SettingsAPI --> UserSettings[User Settings]
        SettingsAPI --> AppSettings[App Settings]
    end

    API -->|Internal Calls| Backend

    subgraph "Backend (Python)"
        Backend[Backend Services] --> ProjectManager[Project Manager]
        Backend --> TaskManager[Task Manager]
        Backend --> LLMService[LLM Service]
        Backend --> VisualizationService[Visualization Service]
        Backend --> ConfigManager[Configuration Manager]

        ProjectManager --> FileSystem[File System Access]
        ProjectManager --> ProjectIndexing[Project Indexing]

        TaskManager --> TaskStorage[Task Storage]
        TaskManager --> TaskAnalysis[Task Analysis]

        LLMService --> ProviderIntegration[Provider Integration]
        LLMService --> PromptManagement[Prompt Management]

        VisualizationService --> GraphAlgorithms[Graph Algorithms]
        VisualizationService --> ImageGeneration[Image Generation]

        ConfigManager --> SettingsStorage[Settings Storage]
        ConfigManager --> EnvironmentVariables[Environment Variables]
    end

    Backend -->|File Operations| FileStorage[File-based Storage]

    subgraph "Storage"
        FileStorage --> VibeArchFolder[.VibeArch Folder]

        VibeArchFolder --> DirectoryFolder[Directory]
        VibeArchFolder --> SpecificationFolder[Specification]
        VibeArchFolder --> ArchitectureFolder[Architecture]
        VibeArchFolder --> TechStackFolder[TechStack]
        VibeArchFolder --> DocumentationFolder[Documentation]

        DirectoryFolder --> IndexFile[index.json]
        DirectoryFolder --> MetadataFile[metadata.json]

        SpecificationFolder --> MainSpecFile[main.md]
        SpecificationFolder --> HistoryFolder[history/]

        ArchitectureFolder --> OverviewFile[overview.md]
        ArchitectureFolder --> ComponentsFile[components.json]
        ArchitectureFolder --> DiagramsFolder[diagrams/]

        TechStackFolder --> StackFile[stack.json]
        TechStackFolder --> ConfigFolder[config/]

        DocumentationFolder --> ReadmeFile[readme.md]
        DocumentationFolder --> APIFolder[api/]
        DocumentationFolder --> UserFolder[user/]
    end

    classDef frontend fill:#42a5f5,stroke:#1976d2,color:white;
    classDef api fill:#7e57c2,stroke:#4527a0,color:white;
    classDef backend fill:#26a69a,stroke:#00796b,color:white;
    classDef storage fill:#ef5350,stroke:#c62828,color:white;

    class Frontend,NavBar,Sidebar,MainContent,Dashboard,Projects,Tasks,LLM,Visualization,Settings,ProjectStats,RecentProjects,ProjectList,ProjectDetails,ProjectCreation,TaskList,TaskDetails,TaskCreation,PromptInput,ModelSelection,ResponseDisplay,GraphView,ExportOptions,APIKeys,Preferences frontend;
    class API,ProjectsAPI,TasksAPI,LLMAPI,VisualizationAPI,SettingsAPI,ProjectsCRUD,ProjectScanning,TasksCRUD,TaskDependencies,TextGeneration,ModelManagement,GraphGeneration,GraphExport,UserSettings,AppSettings api;
    class Backend,ProjectManager,TaskManager,LLMService,VisualizationService,ConfigManager,FileSystem,ProjectIndexing,TaskStorage,TaskAnalysis,ProviderIntegration,PromptManagement,GraphAlgorithms,ImageGeneration,SettingsStorage,EnvironmentVariables backend;
    class FileStorage,VibeArchFolder,DirectoryFolder,SpecificationFolder,ArchitectureFolder,TechStackFolder,DocumentationFolder,IndexFile,MetadataFile,MainSpecFile,HistoryFolder,OverviewFile,ComponentsFile,DiagramsFolder,StackFile,ConfigFolder,ReadmeFile,APIFolder,UserFolder storage;
```
