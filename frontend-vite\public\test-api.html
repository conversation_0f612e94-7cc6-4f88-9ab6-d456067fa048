<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #f5f5f5;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>API Connection Test</h1>
    <div>
        <label for="apiUrl">API URL:</label>
        <input type="text" id="apiUrl" value="http://localhost:8080/health" style="width: 300px;">
        <button onclick="testApi()">Test Connection</button>
    </div>
    <div id="result" class="result">
        <p>Click "Test Connection" to check the API.</p>
    </div>

    <script>
        async function testApi() {
            const apiUrl = document.getElementById('apiUrl').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<p>Testing connection to: ' + apiUrl + '</p>';
            
            try {
                console.log('Fetching:', apiUrl);
                const response = await fetch(apiUrl);
                const data = await response.json();
                
                console.log('Response:', data);
                
                resultDiv.innerHTML += '<p class="success">Connection successful!</p>';
                resultDiv.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML += '<p class="error">Connection failed: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
