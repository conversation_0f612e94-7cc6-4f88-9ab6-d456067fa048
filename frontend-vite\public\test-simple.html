<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple API Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
            background-color: white;
        }
        h1 {
            color: #2563eb;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        input, select {
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin-right: 8px;
            font-size: 14px;
            width: 100%;
            margin-bottom: 10px;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            overflow-x: auto;
        }
        .success {
            color: #16a34a;
        }
        .error {
            color: #dc2626;
        }
        pre {
            background-color: #1e293b;
            color: #e2e8f0;
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Simple API Test</h1>

    <div class="card">
        <h2>API Settings</h2>
        <div>
            <label for="apiUrl">API URL:</label>
            <input type="text" id="apiUrl" value="http://localhost:5001/api">
        </div>
        <div>
            <label for="testPath">Test Path:</label>
            <div style="display: flex; gap: 0.5rem;">
                <input type="text" id="testPath" placeholder="Select a project folder">
                <button id="browseBtn" style="padding: 8px 16px; background-color: #f3f4f6; border: 1px solid #d1d5db; border-radius: 4px; cursor: pointer;">Browse...</button>
            </div>
        </div>
        <button id="testHealthBtn">Test Health Endpoint</button>
        <button id="testCheckBtn">Test Project Check</button>
    </div>

    <div id="result" class="result">
        <p>Results will appear here</p>
    </div>

    <script>
        // Add browse button functionality
        document.getElementById('browseBtn').addEventListener('click', () => {
            // Use a simple prompt instead of file input to avoid security warnings
            const folderPath = prompt("Enter the path to your project folder:", "C:/Users/<USER>/Documents/VSCode/Vibearch");
            if (folderPath) {
                document.getElementById('testPath').value = folderPath;
            }
        });

        document.getElementById('testHealthBtn').addEventListener('click', async () => {
            const apiUrl = document.getElementById('apiUrl').value;
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = '<p>Testing health endpoint...</p>';

            try {
                const healthUrl = `${apiUrl.replace('/api', '')}/health`;
                console.log('Fetching health endpoint:', healthUrl);

                const response = await fetch(healthUrl);
                const data = await response.json();

                console.log('Health response:', data);

                resultDiv.innerHTML = `
                    <p class="success">Health check successful!</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('Error testing health endpoint:', error);
                resultDiv.innerHTML = `
                    <p class="error">Error testing health endpoint: ${error.message}</p>
                `;
            }
        });

        document.getElementById('testCheckBtn').addEventListener('click', async () => {
            const apiUrl = document.getElementById('apiUrl').value;
            const testPath = document.getElementById('testPath').value;
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = '<p>Testing project check endpoint...</p>';

            try {
                const checkUrl = `${apiUrl}/projects/check`;
                console.log('Fetching project check endpoint:', checkUrl);
                console.log('With path:', testPath);

                const response = await fetch(checkUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ path: testPath })
                });

                const data = await response.json();

                console.log('Project check response:', data);

                resultDiv.innerHTML = `
                    <p class="success">Project check successful!</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('Error testing project check endpoint:', error);
                resultDiv.innerHTML = `
                    <p class="error">Error testing project check endpoint: ${error.message}</p>
                `;
            }
        });
    </script>
</body>
</html>
