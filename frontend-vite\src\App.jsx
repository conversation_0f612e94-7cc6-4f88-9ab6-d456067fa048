import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { useState, useEffect } from 'react'
import './index.css'
import './App.css'

// Components
import Layout from './components/Layout'
import AppLayout from './components/AppLayout'
import Dashboard from './components/Dashboard'
import Projects from './components/Projects'
import Architecture from './components/Architecture'
import Directory from './components/Directory'
import MVCD from './components/MVCD'
import ProjectSelection from './components/ProjectSelection'
import ApiTest from './components/ApiTest'
import ClearStorage from './components/ClearStorage'

// Context
import { ProjectProvider, ProjectContext } from './contexts/ProjectContext'
import { useProject } from './hooks/useProject'
import { CodingAgentProvider } from './contexts/CodingAgentContext'
import { NotificationProvider } from './contexts/NotificationContext'

// Placeholder components for routes we haven't implemented yet
const Tasks = () => (
  <div className="py-6">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
      <h1 className="text-2xl font-semibold text-gray-900">Tasks</h1>
      <div className="py-4">
        <div className="bg-white shadow rounded-lg p-6">
          <p>Task management functionality will be implemented here.</p>
        </div>
      </div>
    </div>
  </div>
)

const LLMInterface = () => (
  <div className="py-6">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
      <h1 className="text-2xl font-semibold text-gray-900">LLM Interface</h1>
      <div className="py-4">
        <div className="bg-white shadow rounded-lg p-6">
          <p>LLM integration functionality will be implemented here.</p>
        </div>
      </div>
    </div>
  </div>
)

const Visualization = () => (
  <div className="py-6">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
      <h1 className="text-2xl font-semibold text-gray-900">Visualization</h1>
      <div className="py-4">
        <div className="bg-white shadow rounded-lg p-6">
          <p>Graph visualization functionality will be implemented here.</p>
        </div>
      </div>
    </div>
  </div>
)

const Settings = () => (
  <div className="py-6">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
      <h1 className="text-2xl font-semibold text-gray-900">Settings</h1>
      <div className="py-4">
        <div className="bg-white shadow rounded-lg p-6">
          <p>Application settings will be implemented here.</p>
        </div>
      </div>
    </div>
  </div>
)

// Main application with project context
const AppContent = () => {
  const { currentProject } = useProject();

  return (
    <AppLayout>
      {!currentProject ? (
        // If no project is selected, show the project selection screen
        <ProjectSelection />
      ) : (
        // Otherwise, show the main application with routing
        <Routes>
          <Route path="/" element={<Layout><Dashboard /></Layout>} />
          <Route path="/projects" element={<Layout><Projects /></Layout>} />
          <Route path="/directory" element={<Layout><Directory /></Layout>} />
          <Route path="/mvcd" element={<Layout><MVCD /></Layout>} />
          <Route path="/tasks" element={<Layout><Tasks /></Layout>} />
          <Route path="/llm" element={<Layout><LLMInterface /></Layout>} />
          <Route path="/visualization" element={<Layout><Visualization /></Layout>} />
          <Route path="/architecture" element={<Layout><Architecture /></Layout>} />
          <Route path="/settings" element={<Layout><Settings /></Layout>} />
          <Route path="/api-test" element={<Layout><ApiTest /></Layout>} />
          <Route path="/clear-storage" element={<Layout><ClearStorage /></Layout>} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      )}
    </AppLayout>
  );
}

function App() {
  return (
    <Router>
      <NotificationProvider>
        <CodingAgentProvider>
          <ProjectProvider>
            <AppContent />
          </ProjectProvider>
        </CodingAgentProvider>
      </NotificationProvider>
    </Router>
  )
}

export default App
