import { useEffect, useState } from 'react';

/**
 * IMPORTANT: This component has been disabled to prevent exposing sensitive environment variables.
 * It should only be used during development and never in production.
 *
 * If you need to debug environment variables, use the browser console or server logs.
 */
function EnvTest() {
  const [mode, setMode] = useState('');
  const [isDev, setIsDev] = useState(false);

  useEffect(() => {
    // Only collect non-sensitive environment variables
    setMode(import.meta.env.MODE || 'unknown');
    setIsDev(import.meta.env.DEV || false);
  }, []);

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Application Mode</h1>
      <div className="bg-gray-100 p-4 rounded">
        <h2 className="text-xl mb-2">Current Environment:</h2>
        <div className="bg-white p-2 rounded">
          <p><strong>Mode:</strong> {mode}</p>
          <p><strong>Development:</strong> {isDev ? 'Yes' : 'No'}</p>
          <p className="text-red-600 mt-4">
            Note: For security reasons, API keys and other sensitive environment variables are not displayed.
          </p>
        </div>
      </div>
    </div>
  );
}

// This component is disabled and should not be used in production
export default EnvTest;
