import { useState, useEffect, useRef } from 'react';
import mermaid from 'mermaid';
import { useProject } from '../hooks/useProject';

// Initialize mermaid
mermaid.initialize({
  startOnLoad: false,
  theme: 'default',
  securityLevel: 'loose',
  flowchart: {
    useMaxWidth: false,
    htmlLabels: true
  }
});

const Architecture = () => {
  const { currentProject } = useProject();
  const [activeTab, setActiveTab] = useState('system');
  const [diagrams, setDiagrams] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [zoomLevels, setZoomLevels] = useState({
    system: 1,
    frontend: 1,
    backend: 1,
    dataflow: 1
  });

  const diagramRefs = {
    system: useRef(null),
    frontend: useRef(null),
    backend: useRef(null),
    dataflow: useRef(null)
  };

  // Function to fetch diagram content from project's .vibearch folder or fallback to public directory
  const fetchDiagram = async (name) => {
    try {
      console.log(`Fetching diagram: ${name}`);

      // First try to load from the current project's .vibearch folder if a project is selected
      if (currentProject) {
        try {
          const projectPath = encodeURIComponent(currentProject.path);
          const response = await fetch(`/.VibeArch/Architecture/diagrams/${name}.md?project=${projectPath}`);

          if (response.ok) {
            const text = await response.text();
            console.log(`Received ${name} diagram content from project .vibearch folder`);

            // Extract mermaid code from markdown
            const mermaidMatch = text.match(/```mermaid\n([\s\S]*?)```/);
            return mermaidMatch ? mermaidMatch[1] : '';
          }
          // If not found in project, continue to fallback
          console.log(`Diagram not found in project .vibearch folder, trying public directory`);
        } catch (projectErr) {
          console.error(`Error fetching from project .vibearch folder:`, projectErr);
          // Continue to fallback
        }
      }

      // Fallback to public directory
      const response = await fetch(`/diagrams/${name}.md`);
      if (!response.ok) {
        throw new Error(`Failed to fetch ${name} diagram: ${response.status} ${response.statusText}`);
      }
      const text = await response.text();
      console.log(`Received ${name} diagram content from public directory`);

      // Extract mermaid code from markdown
      const mermaidMatch = text.match(/```mermaid\n([\s\S]*?)```/);
      return mermaidMatch ? mermaidMatch[1] : '';
    } catch (err) {
      console.error(`Error fetching ${name} diagram:`, err);
      setError(`Failed to load ${name} diagram. ${err.message}`);
      return '';
    }
  };

  // Load diagrams on component mount or when current project changes
  useEffect(() => {
    const loadDiagrams = async () => {
      setLoading(true);
      try {
        const [systemDiagram, frontendDiagram, backendDiagram, dataFlowDiagram] = await Promise.all([
          fetchDiagram('system_architecture'),
          fetchDiagram('frontend_architecture'),
          fetchDiagram('backend_architecture'),
          fetchDiagram('data_flow')
        ]);

        console.log('Loaded diagrams:');
        console.log('System diagram length:', systemDiagram.length);
        console.log('Frontend diagram length:', frontendDiagram.length);
        console.log('Backend diagram length:', backendDiagram.length);
        console.log('Data flow diagram length:', dataFlowDiagram.length);

        setDiagrams({
          system: systemDiagram,
          frontend: frontendDiagram,
          backend: backendDiagram,
          dataflow: dataFlowDiagram
        });

        setError(null);
      } catch (err) {
        console.error('Error loading diagrams:', err);
        setError('Failed to load diagrams. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    loadDiagrams();
  }, [currentProject, fetchDiagram]);

  // Render diagrams when they are loaded or when the active tab changes
  useEffect(() => {
    if (!loading && diagrams[activeTab]) {
      const renderDiagram = async () => {
        try {
          console.log(`Rendering ${activeTab} diagram...`);
          console.log(`Diagram content length: ${diagrams[activeTab].length}`);

          const element = diagramRefs[activeTab].current;
          if (element) {
            element.innerHTML = '';
            const { svg } = await mermaid.render(`mermaid-${activeTab}`, diagrams[activeTab]);
            element.innerHTML = svg;
            console.log(`Successfully rendered ${activeTab} diagram`);

            // Apply zoom
            const svgElement = element.querySelector('svg');
            if (svgElement) {
              svgElement.style.transform = `scale(${zoomLevels[activeTab]})`;
              svgElement.style.transformOrigin = 'top left';
              console.log(`Applied zoom level ${zoomLevels[activeTab]} to ${activeTab} diagram`);
            }
          }
        } catch (err) {
          console.error(`Error rendering ${activeTab} diagram:`, err);
          setError(`Failed to render ${activeTab} diagram. ${err.message}`);
        }
      };

      renderDiagram();
    }
  }, [loading, diagrams, activeTab, zoomLevels]);

  // Zoom functions
  const zoomIn = () => {
    setZoomLevels(prev => ({
      ...prev,
      [activeTab]: prev[activeTab] * 1.2
    }));
  };

  const zoomOut = () => {
    setZoomLevels(prev => ({
      ...prev,
      [activeTab]: prev[activeTab] * 0.8
    }));
  };

  const resetZoom = () => {
    setZoomLevels(prev => ({
      ...prev,
      [activeTab]: 1
    }));
  };

  // Tab descriptions
  const tabDescriptions = {
    system: 'High-level overview of the entire Vibe Architect system, showing the relationships between frontend, API, backend, and storage components.',
    frontend: 'Detailed view of the frontend components, including pages, components, services, and hooks.',
    backend: 'Detailed view of the API and backend services, including endpoints, services, models, and storage.',
    dataflow: 'Visualization of how data flows through the system, including user interactions, API requests, and file operations.'
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">Architecture</h1>
        {currentProject && (
          <p className="text-sm text-gray-500 mt-1">
            Project: {currentProject.name} ({currentProject.path})
          </p>
        )}
        <div className="py-4">
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('system')}
                className={`${
                  activeTab === 'system'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                System Architecture
              </button>
              <button
                onClick={() => setActiveTab('frontend')}
                className={`${
                  activeTab === 'frontend'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Frontend Architecture
              </button>
              <button
                onClick={() => setActiveTab('backend')}
                className={`${
                  activeTab === 'backend'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Backend Architecture
              </button>
              <button
                onClick={() => setActiveTab('dataflow')}
                className={`${
                  activeTab === 'dataflow'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Data Flow
              </button>
            </nav>
          </div>

          {/* Description */}
          <div className="mt-4 p-4 bg-blue-50 border-l-4 border-blue-500 rounded-md">
            <p className="text-sm text-blue-700">{tabDescriptions[activeTab]}</p>
          </div>

          {/* Zoom Controls */}
          <div className="mt-4 flex space-x-2">
            <button
              onClick={zoomIn}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Zoom In
            </button>
            <button
              onClick={zoomOut}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Zoom Out
            </button>
            <button
              onClick={resetZoom}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Reset Zoom
            </button>
            <button
              onClick={() => {
                console.log('Current state:');
                console.log('Active tab:', activeTab);
                console.log('Diagrams:', diagrams);
                console.log('Loading:', loading);
                console.log('Error:', error);
                console.log('Zoom levels:', zoomLevels);
                console.log('Diagram refs:', diagramRefs);

                // Try to render the current diagram
                if (diagrams[activeTab]) {
                  console.log(`Attempting to render ${activeTab} diagram...`);
                  mermaid.render(`mermaid-test-${activeTab}`, diagrams[activeTab])
                    .then(result => {
                      console.log('Render successful:', result);
                    })
                    .catch(err => {
                      console.error('Render failed:', err);
                    });
                }
              }}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Debug
            </button>
          </div>

          {/* Diagram Container */}
          <div className="mt-4 bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              {loading ? (
                <div className="flex justify-center items-center h-96">
                  <div className="text-gray-500">Loading diagram...</div>
                </div>
              ) : error ? (
                <div className="flex justify-center items-center h-96">
                  <div className="text-red-500">{error}</div>
                </div>
              ) : (
                <div className="overflow-auto" style={{ maxHeight: '600px' }}>
                  <div ref={diagramRefs.system} style={{ display: activeTab === 'system' ? 'block' : 'none' }}>
                    {activeTab === 'system' && !diagrams.system && <pre>No system diagram content available</pre>}
                  </div>
                  <div ref={diagramRefs.frontend} style={{ display: activeTab === 'frontend' ? 'block' : 'none' }}>
                    {activeTab === 'frontend' && !diagrams.frontend && <pre>No frontend diagram content available</pre>}
                  </div>
                  <div ref={diagramRefs.backend} style={{ display: activeTab === 'backend' ? 'block' : 'none' }}>
                    {activeTab === 'backend' && !diagrams.backend && <pre>No backend diagram content available</pre>}
                  </div>
                  <div ref={diagramRefs.dataflow} style={{ display: activeTab === 'dataflow' ? 'block' : 'none' }}>
                    {activeTab === 'dataflow' && !diagrams.dataflow && <pre>No data flow diagram content available</pre>}
                  </div>

                  {/* Fallback content display */}
                  {error && (
                    <div className="mt-4 p-4 bg-gray-100 rounded">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Raw Diagram Content:</h3>
                      <pre className="text-xs overflow-auto p-2 bg-gray-50 border border-gray-200 rounded" style={{ maxHeight: '300px' }}>
                        {diagrams[activeTab] || 'No content available'}
                      </pre>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Architecture;
