import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useProject } from '../hooks/useProject';
import { useCodingAgent } from '../contexts/CodingAgentContext';

const Dashboard = () => {
  const [baseDirectory, setBaseDirectory] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    totalProjects: 0,
    totalTasks: 0,
    llmInteractions: 0
  });

  // Get coding agent from context
  const { codingAgent, setCodingAgent } = useCodingAgent();

  const { currentProject, recentProjects, loadProject } = useProject();

  useEffect(() => {
    // Update stats when recentProjects changes
    loadProjectData();
  }, [recentProjects]);

  const loadProjectData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Calculate stats based on recentProjects from context
      setStats({
        totalProjects: recentProjects.length,
        totalTasks: 0, // This would come from a real API
        llmInteractions: 0 // This would come from a real API
      });
    } catch (err) {
      console.error('Error loading project data:', err);
      setError('Failed to load project data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle coding agent selection change
  const handleCodingAgentChange = (e) => {
    const newAgent = e.target.value;
    setCodingAgent(newAgent);
    console.log('Dashboard: Coding agent changed to:', newAgent);
  };

  const handleDirectorySelect = async () => {
    if (!baseDirectory) return;

    setIsLoading(true);
    setError(null);

    try {
      // In a real implementation, this would call the backend API
      // to scan the directory and return project information
      console.log(`Scanning directory: ${baseDirectory}`);

      // For now, we'll just add a delay to simulate the API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // After scanning, reload project data
      await loadProjectData();
    } catch (err) {
      console.error('Error scanning directory:', err);
      setError(`Error scanning directory: ${err.message || err}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>

          {/* Coding Agent Selection */}
          <div className="flex items-center bg-white px-4 py-2 rounded-lg shadow-sm">
            <label htmlFor="dashboard-coding-agent" className="mr-2 text-sm font-medium text-gray-700">
              Preferred Coding Agent:
            </label>
            <select
              id="dashboard-coding-agent"
              value={codingAgent}
              onChange={handleCodingAgentChange}
              className="block w-40 pl-3 pr-10 py-1 text-base border-gray-300 focus:outline-none focus:ring-gray-500 focus:border-gray-500 sm:text-sm rounded-md"
              title="Select which coding agent to use for enriching MVCD descriptions"
            >
              <option value="augment">Augment</option>
              <option value="cursor">Cursor</option>
              <option value="chatgpt">ChatGPT</option>
            </select>
            <div className="ml-2 text-gray-400 hover:text-gray-600 cursor-help" title="The selected coding agent will be used to analyze code and generate descriptions. Make sure you have access to the selected agent.">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <div className="py-4">
          {/* Project Directory Selection */}
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Select Project Directory</h2>
            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="text"
                value={baseDirectory}
                onChange={(e) => setBaseDirectory(e.target.value)}
                placeholder="Enter directory path"
                className="shadow-sm focus:ring-gray-500 focus:border-gray-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
              <button
                onClick={handleDirectorySelect}
                disabled={isLoading || !baseDirectory}
                className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${
                  isLoading || !baseDirectory
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500'
                }`}
              >
                {isLoading ? 'Loading...' : 'Scan Directory'}
              </button>
              <button
                onClick={() => {
                  // In a real implementation, this would use a file dialog
                  // For now, we'll use a simple prompt
                  const path = prompt("Enter directory path:");
                  if (path) {
                    setBaseDirectory(path);
                  }
                }}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Browse...
              </button>
            </div>
            {error && (
              <div className="mt-4 p-4 bg-red-50 border-l-4 border-red-500 rounded-md">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 mb-6">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-gray-500 rounded-md p-3">
                    <svg className="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                    </svg>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Projects</dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900">{stats.totalProjects}</div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-gray-600 rounded-md p-3">
                    <svg className="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Tasks</dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900">{stats.totalTasks}</div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-gray-700 rounded-md p-3">
                    <svg className="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                    </svg>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">LLM Interactions</dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900">{stats.llmInteractions}</div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Projects */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Recent Projects</h3>
              <Link to="/projects" className="text-sm font-medium text-gray-600 hover:text-gray-500">
                View all
              </Link>
            </div>
            <div className="border-t border-gray-200">
              <ul className="divide-y divide-gray-200">
                {recentProjects && recentProjects.length > 0 ? (
                  recentProjects.map((project, index) => (
                    <li key={project.path || index}>
                      <div className="px-4 py-4 sm:px-6 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-md flex items-center justify-center">
                              <svg className="h-6 w-6 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                              </svg>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-600">{project.name || project.path.split(/[/\\]/).pop()}</div>
                              <div className="text-sm text-gray-500">{project.path}</div>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <button
                              type="button"
                              onClick={async (e) => {
                                e.preventDefault();
                                console.log('Dashboard: Opening project:', project.path);
                                try {
                                  // Show loading state
                                  setIsLoading(true);

                                  // Create a simple project object with the path
                                  // This is a fallback in case the API call fails
                                  const simpleProject = {
                                    path: project.path,
                                    name: project.name || project.path.split(/[/\\]/).pop(),
                                    created: new Date().toISOString()
                                  };

                                  try {
                                    // Try to load the project from the API
                                    const loadedProject = await loadProject(project.path);

                                    if (loadedProject) {
                                      console.log('Dashboard: Project loaded successfully from API:', loadedProject);
                                    } else {
                                      // If API loading fails, use the simple project object
                                      console.warn('Dashboard: API loading failed, using simple project object');
                                      localStorage.setItem('currentProject', JSON.stringify(simpleProject));
                                    }
                                  } catch (apiError) {
                                    console.error('Dashboard: API error loading project:', apiError);
                                    // If API loading fails, use the simple project object
                                    localStorage.setItem('currentProject', JSON.stringify(simpleProject));
                                  }

                                  // Reload the page to refresh the dashboard with the loaded project
                                  window.location.reload();
                                } catch (error) {
                                  console.error('Dashboard: Error opening project:', error);
                                  setError(`Error opening project: ${error.message || 'Unknown error'}`);
                                } finally {
                                  setIsLoading(false);
                                }
                              }}
                              className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                              disabled={isLoading}
                            >
                              {isLoading ? 'Opening...' : 'Open'}
                            </button>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))
                ) : (
                  <li>
                    <div className="px-4 py-8 text-center text-gray-500">
                      {isLoading ? (
                        <p>Loading projects...</p>
                      ) : error ? (
                        <p className="text-red-500">{error}</p>
                      ) : (
                        <p>No projects found. Select a directory to scan for projects.</p>
                      )}
                    </div>
                  </li>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
