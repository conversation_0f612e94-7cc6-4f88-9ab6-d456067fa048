import { useState, useEffect } from 'react';
import { useProject } from '../hooks/useProject';
import api from '../services/api';

const Directory = () => {
  const { currentProject } = useProject();
  const [directoryStructure, setDirectoryStructure] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [expandedFolders, setExpandedFolders] = useState({});

  useEffect(() => {
    const fetchDirectoryStructure = async () => {
      if (!currentProject?.path) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        console.log('Fetching directory structure for:', currentProject.path);

        // Build the directory tree recursively
        const buildDirectoryTree = async (path) => {
          try {
            // Get directories and files at this path
            const response = await api.get(`/directory/list?path=${encodeURIComponent(path)}`);
            const entries = response.data.directories || [];

            // Create the node for this path
            const pathParts = path.split(/[/\\]/);
            const name = pathParts[pathParts.length - 1] || path;

            const node = {
              name: name,
              path: path,
              type: 'folder',
              children: []
            };

            // Process each entry
            for (const entry of entries) {
              if (entry.isDirectory) {
                // Recursively process directories
                const childNode = await buildDirectoryTree(entry.path);
                if (childNode) {
                  node.children.push(childNode);
                }
              } else {
                // Add files directly
                node.children.push({
                  name: entry.name,
                  path: entry.path,
                  type: 'file'
                });
              }
            }

            return node;
          } catch (err) {
            console.error(`Error building directory tree for ${path}:`, err);
            return null;
          }
        };

        // Start building the tree from the project root
        const directoryTree = await buildDirectoryTree(currentProject.path);
        console.log('Directory tree:', directoryTree);

        if (directoryTree) {
          setDirectoryStructure(directoryTree);
        } else {
          throw new Error('Failed to build directory tree');
        }
      } catch (err) {
        console.error('Error fetching directory structure:', err);
        setError(`Error fetching directory structure: ${err.message || err}`);
      } finally {
        setLoading(false);
      }
    };

    fetchDirectoryStructure();
  }, [currentProject]);

  const toggleFolder = (path) => {
    setExpandedFolders(prev => ({
      ...prev,
      [path]: !prev[path]
    }));
  };

  const renderTree = (node, level = 0) => {
    if (!node) return null;

    const isExpanded = expandedFolders[node.path] !== false; // Default to expanded

    if (node.type === 'folder') {
      return (
        <div key={node.path} style={{ marginLeft: `${level * 20}px` }}>
          <div
            className="flex items-center py-1 cursor-pointer hover:bg-gray-100"
            onClick={() => toggleFolder(node.path)}
          >
            <span className="mr-2">
              {isExpanded ? (
                <svg className="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              ) : (
                <svg className="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              )}
            </span>
            <svg className="h-5 w-5 text-yellow-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
            </svg>
            <span className="font-medium">{node.name}</span>
          </div>
          {isExpanded && node.children && node.children.map(child => renderTree(child, level + 1))}
        </div>
      );
    } else {
      return (
        <div key={node.path} style={{ marginLeft: `${level * 20}px` }} className="flex items-center py-1">
          <span className="mr-2 w-4"></span>
          <svg className="h-5 w-5 text-gray-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <span>{node.name}</span>
        </div>
      );
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Project Directory</h1>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500">Loading directory structure...</div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
          <div className="text-red-700">{error}</div>
        </div>
      ) : !currentProject ? (
        <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4 mb-4">
          <div className="text-yellow-700">No project selected. Please select a project first.</div>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="border border-gray-200 rounded-md p-4">
              {directoryStructure && renderTree(directoryStructure)}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Directory;
