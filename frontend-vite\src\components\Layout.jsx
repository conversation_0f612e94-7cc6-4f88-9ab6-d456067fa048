import { useState, useEffect } from 'react';
import Sidebar from './common/Sidebar';

const Layout = ({ children }) => {
  const [apiStatus, setApiStatus] = useState('checking');
  const [healthUrl] = useState('/health');

  useEffect(() => {
    const checkApiConnection = async () => {
      try {
        const response = await fetch(healthUrl);
        const data = await response.json();

        if (data && data.status === 'healthy') {
          setApiStatus('connected');
        } else {
          setApiStatus('error');
        }
      } catch (error) {
        console.error('API connection error:', error);
        setApiStatus('error');
      }
    };

    checkApiConnection();

    // Check connection every 30 seconds
    const interval = setInterval(checkApiConnection, 30000);

    // Clean up interval on component unmount
    return () => clearInterval(interval);
  }, [healthUrl]);

  return (
    <div className="h-screen flex overflow-hidden bg-white">
      <Sidebar />
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        <div className="relative z-10 flex-shrink-0 flex h-16 bg-white shadow justify-end">
          {/* API Status Indicator */}
          <div className="h-16 flex items-center px-4">
            <div className="flex items-center">
              <div
                className={`h-3 w-3 rounded-full mr-2 ${
                  apiStatus === 'connected'
                    ? 'bg-green-500'
                    : apiStatus === 'checking'
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
                }`}
              ></div>
              <span className="text-sm text-gray-500">
                {apiStatus === 'connected'
                  ? 'API Connected'
                  : apiStatus === 'checking'
                  ? 'Checking API...'
                  : 'API Disconnected'}
              </span>
            </div>
          </div>
        </div>
        <main className="flex-1 relative overflow-y-auto focus:outline-none bg-white">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;
