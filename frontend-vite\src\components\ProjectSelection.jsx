import { useState, useContext } from 'react';
import { useProject } from '../hooks/useProject';
import { ProjectContext } from '../contexts/ProjectContext';
import FileBrowser from './FileBrowser';

const ProjectSelection = () => {
  const [projectPath, setProjectPath] = useState('');
  const [projectName, setProjectName] = useState('');
  const [showInitForm, setShowInitForm] = useState(false);
  const [localError, setLocalError] = useState(null);
  const [isLocalLoading, setIsLocalLoading] = useState(false);
  const [showFileBrowser, setShowFileBrowser] = useState(false);

  // Get context directly to access setCurrentProject
  const projectContext = useContext(ProjectContext);

  const {
    recentProjects,
    isLoading,
    error,
    checkProjectDirectory,
    initializeProject,
    loadProject,
    setCurrentProject
  } = useProject();

  const handleProjectSelect = async (e) => {
    e.preventDefault();
    console.log('handleProjectSelect called with path:', projectPath);
    setLocalError(null);

    if (!projectPath) {
      setLocalError('Please select a project directory');
      return;
    }

    try {
      setIsLocalLoading(true);

      // Check if the directory has a .vibearch folder
      const hasVibeArchFolder = await checkForVibeArchFolder(projectPath);

      if (hasVibeArchFolder) {
        // If it has a .vibearch folder, load the project
        const project = {
          name: projectPath.split(/[/\\]/).pop(),
          path: projectPath,
          created: new Date().toISOString()
        };

        // Add to recent projects
        const updatedRecentProjects = [
          project,
          ...recentProjects.filter(p => p.path !== project.path)
        ].slice(0, 10);

        // Store in localStorage
        localStorage.setItem('recentProjects', JSON.stringify(updatedRecentProjects));
        localStorage.setItem('currentProject', JSON.stringify(project));

        // Update state
        setCurrentProject(project);

        console.log('Project loaded successfully:', project);
      } else {
        // If it doesn't have a .vibearch folder, show the initialization form
        setShowInitForm(true);
      }
    } catch (err) {
      console.error('Error checking project directory:', err);
      setLocalError(`Error checking project directory: ${err.message || err}`);
    } finally {
      setIsLocalLoading(false);
    }
  };

  // Helper function to check if a directory has a .vibearch folder
  const checkForVibeArchFolder = async (directoryPath) => {
    try {
      // In a real implementation with Electron, we would use fs.existsSync
      // For now, we'll simulate this check with an API call

      console.log(`Checking for .vibearch folder in ${directoryPath}`);

      // Simulate API call to check for .vibearch folder
      // In a real implementation, this would be an actual API call
      return new Promise((resolve) => {
        // Simulate a delay
        setTimeout(() => {
          // For demo purposes, assume any path with "Vibearch" has a .vibearch folder
          // In a real implementation, this would check if directoryPath + '/.vibearch' exists
          const hasVibeArchFolder = directoryPath.includes('Vibearch');
          console.log(`.vibearch folder ${hasVibeArchFolder ? 'found' : 'not found'} in ${directoryPath}`);
          resolve(hasVibeArchFolder);
        }, 500);
      });
    } catch (err) {
      console.error('Error checking for .vibearch folder:', err);
      throw err;
    }
  };

  const handleInitializeProject = async (e) => {
    e.preventDefault();
    setLocalError(null);

    if (!projectPath || !projectName) {
      setLocalError('Project path and name are required');
      return;
    }

    try {
      setIsLocalLoading(true);

      // Create a .vibearch folder in the project directory
      await createVibeArchFolder(projectPath, projectName);

      // Create a project object
      const project = {
        name: projectName,
        path: projectPath,
        created: new Date().toISOString()
      };

      // Add to recent projects
      const updatedRecentProjects = [
        project,
        ...recentProjects.filter(p => p.path !== project.path)
      ].slice(0, 10);

      // Store in localStorage
      localStorage.setItem('recentProjects', JSON.stringify(updatedRecentProjects));
      localStorage.setItem('currentProject', JSON.stringify(project));

      // Update state
      setCurrentProject(project);

      // Also update context directly to ensure it's set
      if (projectContext && projectContext.setCurrentProject) {
        projectContext.setCurrentProject(project);
      }

      console.log('Project initialized successfully:', project);
      setShowInitForm(false);
    } catch (err) {
      console.error('Error initializing project:', err);
      setLocalError(`Error initializing project: ${err.message || err}`);
    } finally {
      setIsLocalLoading(false);
    }
  };

  // Helper function to create a .vibearch folder
  const createVibeArchFolder = async (directoryPath, projectName) => {
    try {
      // In a real implementation, this would use the Electron fs API
      // For now, we'll simulate this
      console.log(`Creating .vibearch folder in ${directoryPath} with project name ${projectName}`);

      // Simulate creating the folder structure
      const vibeArchFolderPath = `${directoryPath}/.vibearch`;
      console.log(`Created folder: ${vibeArchFolderPath}`);

      // Simulate creating the subfolders
      const subfolders = ['Directory', 'Specification', 'Architecture', 'TechStack', 'Documentation'];
      subfolders.forEach(subfolder => {
        console.log(`Created subfolder: ${vibeArchFolderPath}/${subfolder}`);
      });

      // Simulate creating the Architecture/diagrams folder
      console.log(`Created subfolder: ${vibeArchFolderPath}/Architecture/diagrams`);

      // Simulate creating a project.json file
      const projectConfig = {
        name: projectName,
        path: directoryPath,
        created: new Date().toISOString()
      };
      console.log(`Created file: ${vibeArchFolderPath}/project.json with content:`, projectConfig);

      return true;
    } catch (err) {
      console.error('Error creating .vibearch folder:', err);
      throw err;
    }
  };

  const handleRecentProjectClick = async (projectPath) => {
    try {
      setIsLocalLoading(true);
      setLocalError(null);
      console.log('Loading recent project:', projectPath);

      // Check if the directory still exists and has a .vibearch folder
      const hasVibeArchFolder = await checkForVibeArchFolder(projectPath);

      if (hasVibeArchFolder) {
        // If it has a .vibearch folder, load the project
        const project = {
          name: projectPath.split(/[/\\]/).pop(),
          path: projectPath,
          created: new Date().toISOString()
        };

        // Update state
        setCurrentProject(project);

        // Also update context directly to ensure it's set
        if (projectContext && projectContext.setCurrentProject) {
          projectContext.setCurrentProject(project);
        }

        console.log('Recent project loaded successfully:', project);
      } else {
        // If it doesn't have a .vibearch folder, show an error
        throw new Error('Project directory does not have a .vibearch folder');
      }
    } catch (err) {
      console.error('Error loading recent project:', err);
      setLocalError(`Error loading project: ${err.message || err}`);
    } finally {
      setIsLocalLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <div className="p-8 flex flex-col justify-center flex-1">
        <div className="max-w-2xl mx-auto w-full text-center">
          <h3 className="text-2xl font-bold text-blue-800 border-2 border-blue-500 p-4 rounded-lg bg-blue-50 mb-8">
            Select a project to get started
          </h3>
          <div className="text-center mb-4">
            <a
              href="/clear-storage"
              className="text-sm text-blue-600 hover:text-blue-800 underline"
            >
              Clear Project Data
            </a>
          </div>
        </div>

        {/* File Browser Modal */}
        {showFileBrowser && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <FileBrowser
              onSelect={(selectedPath) => {
                setProjectPath(selectedPath);
                setShowFileBrowser(false);
              }}
              onCancel={() => setShowFileBrowser(false)}
            />
          </div>
        )}

        <div className="max-w-2xl mx-auto w-full">
          <div className="bg-white p-8 shadow-lg rounded-lg border border-gray-200">
            {(error || localError) && (
              <div className="mb-6 bg-red-50 border border-red-200 text-red-700 p-4 rounded-md">
                {localError || error}
              </div>
            )}

            {!showInitForm ? (
              <form onSubmit={handleProjectSelect}>
                <div>
                  <label htmlFor="projectPath" className="block text-base font-medium text-gray-700">
                    Project Directory
                  </label>
                  <div className="mt-2 flex gap-2">
                    <input
                      id="projectPath"
                      name="projectPath"
                      type="text"
                      required
                      value={projectPath}
                      onChange={(e) => setProjectPath(e.target.value)}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm text-base focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Select a project folder"
                      readOnly
                    />
                    <button
                      type="button"
                      onClick={() => setShowFileBrowser(true)}
                      className="px-4 py-3 bg-gray-100 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-200"
                    >
                      Browse...
                    </button>
                  </div>
                </div>

                <div className="mt-6">
                  <button
                    type="submit"
                    disabled={isLoading || isLocalLoading || !projectPath}
                    className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-base font-medium text-white ${
                      isLoading || isLocalLoading || !projectPath
                        ? 'bg-blue-300 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    {isLoading || isLocalLoading ? 'Loading...' : 'Open Project'}
                  </button>
                </div>
              </form>
            ) : (
              <form onSubmit={handleInitializeProject}>
                <div>
                  <label htmlFor="projectPath" className="block text-base font-medium text-gray-700">
                    Project Directory
                  </label>
                  <div className="mt-2">
                    <input
                      id="projectPath"
                      name="projectPath"
                      type="text"
                      required
                      value={projectPath}
                      readOnly
                      className="block w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm text-base bg-gray-100"
                    />
                  </div>
                </div>

                <div className="mt-6">
                  <label htmlFor="projectName" className="block text-base font-medium text-gray-700">
                    Project Name
                  </label>
                  <div className="mt-2">
                    <input
                      id="projectName"
                      name="projectName"
                      type="text"
                      required
                      value={projectName}
                      onChange={(e) => setProjectName(e.target.value)}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm text-base focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter project name"
                    />
                  </div>
                </div>

                <div className="mt-6 flex gap-4">
                  <button
                    type="button"
                    onClick={() => setShowInitForm(false)}
                    className="w-1/2 flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading || isLocalLoading || !projectName}
                    className={`w-1/2 flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-base font-medium text-white ${
                      isLoading || isLocalLoading || !projectName
                        ? 'bg-blue-300 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    {isLoading || isLocalLoading ? 'Initializing...' : 'Initialize Project'}
                  </button>
                </div>
              </form>
            )}

            {recentProjects.length > 0 && (
              <div className="mt-8">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300"></div>
                  </div>
                  <div className="relative flex justify-center text-base">
                    <span className="px-3 bg-white text-gray-600 font-medium">Recent Projects</span>
                  </div>
                </div>

                <div className="mt-6 flex flex-col gap-3">
                  {recentProjects.map((project) => (
                    <button
                      key={project.path}
                      onClick={() => handleRecentProjectClick(project.path)}
                      className="w-full text-left p-5 border border-gray-300 rounded-md shadow-sm text-base text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <div className="font-medium">{project.name}</div>
                      <div className="text-sm text-gray-500 truncate mt-1">{project.path}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectSelection;
