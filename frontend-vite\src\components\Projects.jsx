import { useState, useContext, useEffect } from 'react';
import { useProject } from '../hooks/useProject';
import { ProjectContext } from '../contexts/ProjectContext';
import FileBrowser from './FileBrowser';
import { isBackendAvailable, handleApiError, createMinimalProject } from '../utils/apiUtils';
import { useNotification } from '../contexts/NotificationContext';

const Projects = () => {
  const [baseDirectory, setBaseDirectory] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showNewProjectForm, setShowNewProjectForm] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [newProjectPath, setNewProjectPath] = useState('');
  const [showFileBrowser, setShowFileBrowser] = useState(false);
  const [fileBrowserTarget, setFileBrowserTarget] = useState('scan'); // 'scan' or 'new'
  const [backendStatus, setBackendStatus] = useState(true); // Assume backend is available initially

  // Get project context
  const {
    currentProject,
    recentProjects,
    loadProject,
    checkProjectDirectory,
    initializeProject
  } = useProject();

  // Get notification context
  const { showWarning, showError, showInfo } = useNotification();

  // Check backend availability on component mount
  useEffect(() => {
    const checkBackend = async () => {
      const available = await isBackendAvailable();
      setBackendStatus(available);

      if (!available) {
        showWarning(
          'Backend server is not available. Some features may be limited.',
          10000
        );
      }
    };

    checkBackend();

    // Set up periodic backend checks
    const intervalId = setInterval(checkBackend, 30000); // Check every 30 seconds

    return () => clearInterval(intervalId);
  }, [showWarning]);

  // Use projects from the context instead of mock data
  const projectsToDisplay = recentProjects.map((project, index) => ({
    id: index + 1,
    name: project.name || project.path.split(/[/\\]/).pop(),
    path: project.path,
    tasks: project.tasks || 0,
    completedTasks: project.completedTasks || 0,
    lastModified: project.lastModified || project.created || new Date().toISOString().split('T')[0]
  }));

  const handleDirectorySelect = async () => {
    if (!baseDirectory) return;

    setIsLoading(true);
    try {
      // Check if the directory has a .vibearch folder
      const result = await checkProjectDirectory(baseDirectory);

      if (result.hasVibeArchFolder) {
        // If it has a .vibearch folder, load the project
        await loadProject(baseDirectory);
      } else {
        // If it doesn't have a .vibearch folder, show the new project form
        setNewProjectPath(baseDirectory);
        setShowNewProjectForm(true);
      }
    } catch (error) {
      console.error('Error checking directory:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateProject = async (e) => {
    e.preventDefault();
    if (!newProjectName || !newProjectPath) return;

    setIsLoading(true);
    try {
      // Initialize a new project
      await initializeProject(newProjectPath, newProjectName);

      // Reset form
      setShowNewProjectForm(false);
      setNewProjectName('');
      setNewProjectPath('');
    } catch (error) {
      console.error('Error creating project:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="py-6">
      {/* File Browser Modal */}
      {showFileBrowser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <FileBrowser
            onSelect={(selectedPath) => {
              if (fileBrowserTarget === 'scan') {
                setBaseDirectory(selectedPath);
              } else if (fileBrowserTarget === 'new') {
                setNewProjectPath(selectedPath);
              }
              setShowFileBrowser(false);
            }}
            onCancel={() => setShowFileBrowser(false)}
          />
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-semibold text-gray-900">Projects</h1>
          <button
            onClick={() => setShowNewProjectForm(!showNewProjectForm)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {showNewProjectForm ? 'Cancel' : 'New Project'}
          </button>
        </div>
      </div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <div className="py-4">
          {/* Project Directory Selection */}
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Select Project Directory</h2>
            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="text"
                value={baseDirectory}
                onChange={(e) => setBaseDirectory(e.target.value)}
                placeholder="Enter directory path"
                className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
              <button
                onClick={handleDirectorySelect}
                disabled={isLoading || !baseDirectory}
                className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${
                  isLoading || !baseDirectory
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                }`}
              >
                {isLoading ? 'Loading...' : 'Scan Directory'}
              </button>
              <button
                onClick={() => {
                  setFileBrowserTarget('scan');
                  setShowFileBrowser(true);
                }}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Browse...
              </button>
            </div>
          </div>

          {/* New Project Form */}
          {showNewProjectForm && (
            <div className="bg-white shadow rounded-lg p-6 mb-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Create New Project</h2>
              <form onSubmit={handleCreateProject}>
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                  <div className="sm:col-span-3">
                    <label htmlFor="project-name" className="block text-sm font-medium text-gray-700">
                      Project Name
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="project-name"
                        id="project-name"
                        value={newProjectName}
                        onChange={(e) => setNewProjectName(e.target.value)}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-6">
                    <label htmlFor="project-path" className="block text-sm font-medium text-gray-700">
                      Project Path
                    </label>
                    <div className="mt-1 flex rounded-md shadow-sm">
                      <input
                        type="text"
                        name="project-path"
                        id="project-path"
                        value={newProjectPath}
                        onChange={(e) => setNewProjectPath(e.target.value)}
                        className="flex-1 focus:ring-blue-500 focus:border-blue-500 block w-full min-w-0 rounded-none rounded-l-md sm:text-sm border-gray-300"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setFileBrowserTarget('new');
                          setShowFileBrowser(true);
                        }}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-r-md text-gray-700 bg-gray-50 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Browse
                      </button>
                    </div>
                  </div>
                </div>
                <div className="pt-5">
                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={() => setShowNewProjectForm(false)}
                      className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={!newProjectName || !newProjectPath}
                      className={`ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white ${
                        !newProjectName || !newProjectPath
                          ? 'bg-gray-400 cursor-not-allowed'
                          : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                      }`}
                    >
                      Create
                    </button>
                  </div>
                </div>
              </form>
            </div>
          )}

          {/* Projects List */}
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            {projectsToDisplay.length > 0 ? (
              <ul className="divide-y divide-gray-200">
                {projectsToDisplay.map((project) => (
                  <li key={project.id}>
                    <div className="px-4 py-4 flex items-center sm:px-6 hover:bg-gray-50">
                      <div className="min-w-0 flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                          <div className="flex text-sm">
                            <p className="font-medium text-blue-600 truncate">{project.name}</p>
                            <p className="ml-1 flex-shrink-0 font-normal text-gray-500">
                              ({project.path})
                            </p>
                          </div>
                          <div className="mt-2 flex">
                            <div className="flex items-center text-sm text-gray-500">
                              <svg className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                              </svg>
                              <span>Last modified on <time dateTime={project.lastModified}>{project.lastModified}</time></span>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4 flex-shrink-0 sm:mt-0 sm:ml-5">
                          <div className="flex -space-x-1 overflow-hidden">
                            <div className="flex items-center">
                              <span className="text-sm text-gray-500 mr-4">
                                {project.completedTasks}/{project.tasks} tasks completed
                              </span>
                              <button
                                type="button"
                                onClick={async () => {
                                  console.log('Projects: Opening project:', project.path);
                                  try {
                                    // Show loading state
                                    setIsLoading(true);

                                    // First check if the backend is available
                                    const backendAvailable = await isBackendAvailable();

                                    if (!backendAvailable) {
                                      console.warn('Projects: Backend is not available, using minimal project object');

                                      // Create a minimal project object
                                      const minimalProject = createMinimalProject(project.path, project.name);

                                      // Store in localStorage
                                      localStorage.setItem('currentProject', JSON.stringify(minimalProject));

                                      // Show a notification
                                      showWarning('Backend server is not available. Using minimal project information.');

                                      // Navigate to dashboard
                                      window.location.href = '/';
                                      return;
                                    }

                                    try {
                                      // Try to load the project from the API
                                      const loadedProject = await loadProject(project.path);

                                      // The loadProject function now always returns a project object
                                      // (either from the API or a minimal one)
                                      console.log('Projects: Project loaded:', loadedProject);

                                      // Navigate to dashboard
                                      window.location.href = '/';
                                    } catch (apiError) {
                                      // Handle the error with our utility function
                                      const errorMessage = handleApiError(apiError, 'Projects: Error loading project');

                                      // Create a minimal project object as a fallback
                                      const minimalProject = createMinimalProject(project.path, project.name);

                                      // Store in localStorage
                                      localStorage.setItem('currentProject', JSON.stringify(minimalProject));

                                      // Show a notification
                                      showWarning('Using minimal project information due to API error: ' + errorMessage);

                                      // Navigate to dashboard
                                      window.location.href = '/';
                                    }
                                  } catch (error) {
                                    console.error('Projects: Error opening project:', error);
                                    showError(`Error opening project: ${error.message || 'Unknown error'}`);
                                  } finally {
                                    setIsLoading(false);
                                  }
                                }}
                                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                disabled={isLoading}
                              >
                                {isLoading ? 'Opening...' : 'Open'}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="p-6 text-center">
                <p className="text-gray-500">No projects found. Create a new project or select an existing project directory.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Projects;
