import React from 'react';
import { Link } from 'react-router-dom';
// Import the logo directly
import vaLogo from '../../assets/VA-Logo.jpg';

const AppHeader = () => {
  return (
    <header className="w-full px-6 py-4 bg-gradient-to-r from-gray-700 to-gray-900 border-b-2 border-gray-800 shadow-xl">
      <div className="flex items-center max-w-7xl mx-auto">
        <div className="flex items-center">
          <img src={vaLogo} alt="Vibe Architect Logo" className="h-12 w-12 mr-4 rounded-full border-2 border-white shadow-md" />
          <h2 className="text-2xl font-bold text-white">
            Vibe Architect <span className="text-2xl text-gray-300">- System Architect for your Vibe coding</span>
          </h2>
        </div>
      </div>
    </header>
  );
};

export default AppHeader;
