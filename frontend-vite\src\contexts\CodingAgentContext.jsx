import { createContext, useState, useEffect, useContext } from 'react';

// Create the context
export const CodingAgentContext = createContext();

// Create a provider component
export const CodingAgentProvider = ({ children }) => {
  // Get coding agent from localStorage or default to augment
  const [codingAgent, setCodingAgent] = useState(() => {
    const savedAgent = localStorage.getItem('preferredCodingAgent');
    return savedAgent || 'augment';
  });

  // Update localStorage when coding agent changes
  useEffect(() => {
    localStorage.setItem('preferredCodingAgent', codingAgent);
    console.log('CodingAgentContext: Agent updated to', codingAgent);
  }, [codingAgent]);

  // Provide the context value
  return (
    <CodingAgentContext.Provider value={{ codingAgent, setCodingAgent }}>
      {children}
    </CodingAgentContext.Provider>
  );
};

// Custom hook to use the coding agent context
export const useCodingAgent = () => {
  const context = useContext(CodingAgentContext);
  
  if (!context) {
    throw new Error('useCodingAgent must be used within a CodingAgentProvider');
  }
  
  return context;
};

export default CodingAgentContext;
