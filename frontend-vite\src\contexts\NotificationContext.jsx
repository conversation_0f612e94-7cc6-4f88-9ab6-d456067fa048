import { createContext, useState, useContext } from 'react';
import Notification from '../components/Notification';

// Create the notification context
export const NotificationContext = createContext();

/**
 * Provider component for the notification system
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 */
export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);

  /**
   * Add a new notification
   * @param {Object} notification - The notification to add
   * @param {string} notification.message - The message to display
   * @param {string} notification.type - The type of notification (info, warning, error, success)
   * @param {number} notification.duration - How long to show the notification in ms (0 for permanent)
   */
  const addNotification = (notification) => {
    const id = Date.now().toString();
    setNotifications(prev => [...prev, { id, ...notification }]);
    
    // Auto-remove notification after duration (if not permanent)
    if (notification.duration !== 0) {
      setTimeout(() => {
        removeNotification(id);
      }, notification.duration || 5000);
    }
    
    return id;
  };

  /**
   * Remove a notification by ID
   * @param {string} id - The ID of the notification to remove
   */
  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  /**
   * Show an info notification
   * @param {string} message - The message to display
   * @param {number} duration - How long to show the notification in ms (0 for permanent)
   */
  const showInfo = (message, duration = 5000) => {
    return addNotification({ message, type: 'info', duration });
  };

  /**
   * Show a success notification
   * @param {string} message - The message to display
   * @param {number} duration - How long to show the notification in ms (0 for permanent)
   */
  const showSuccess = (message, duration = 5000) => {
    return addNotification({ message, type: 'success', duration });
  };

  /**
   * Show a warning notification
   * @param {string} message - The message to display
   * @param {number} duration - How long to show the notification in ms (0 for permanent)
   */
  const showWarning = (message, duration = 5000) => {
    return addNotification({ message, type: 'warning', duration });
  };

  /**
   * Show an error notification
   * @param {string} message - The message to display
   * @param {number} duration - How long to show the notification in ms (0 for permanent)
   */
  const showError = (message, duration = 5000) => {
    return addNotification({ message, type: 'error', duration });
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        addNotification,
        removeNotification,
        showInfo,
        showSuccess,
        showWarning,
        showError
      }}
    >
      {children}
      <div className="notification-container">
        {notifications.map(notification => (
          <Notification
            key={notification.id}
            message={notification.message}
            type={notification.type}
            duration={notification.duration}
            onClose={() => removeNotification(notification.id)}
          />
        ))}
      </div>
    </NotificationContext.Provider>
  );
};

/**
 * Hook to use the notification context
 * @returns {Object} The notification context
 */
export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

export default NotificationContext;
