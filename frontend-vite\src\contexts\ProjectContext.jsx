import { createContext, useState, useEffect } from 'react';
import { projectApi } from '../services/api';
import { isBackendAvailable, createMinimalProject, handleApiError } from '../utils/apiUtils';

export const ProjectContext = createContext();

export const ProjectProvider = ({ children }) => {
  const [currentProject, setCurrentProject] = useState(null);
  const [recentProjects, setRecentProjects] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load recent projects from localStorage on initial render
  useEffect(() => {
    const storedProjects = localStorage.getItem('recentProjects');
    if (storedProjects) {
      try {
        setRecentProjects(JSON.parse(storedProjects));
      } catch (err) {
        console.error('Error parsing stored projects:', err);
        // If there's an error parsing, reset the storage
        localStorage.removeItem('recentProjects');
      }
    }

    // Load last selected project if available
    const lastProject = localStorage.getItem('currentProject');
    if (lastProject) {
      try {
        setCurrentProject(JSON.parse(lastProject));
      } catch (err) {
        console.error('Error parsing current project:', err);
        localStorage.removeItem('currentProject');
      }
    }
  }, []);

  // Save current project to localStorage whenever it changes
  useEffect(() => {
    if (currentProject) {
      localStorage.setItem('currentProject', JSON.stringify(currentProject));
    }
  }, [currentProject]);

  // Save recent projects to localStorage whenever they change
  useEffect(() => {
    if (recentProjects.length > 0) {
      localStorage.setItem('recentProjects', JSON.stringify(recentProjects));
    }
  }, [recentProjects]);

  // Check if a directory has a .vibearch folder
  const checkProjectDirectory = async (directoryPath) => {
    console.log('ProjectContext: checkProjectDirectory called with path:', directoryPath);
    setIsLoading(true);
    setError(null);

    try {
      console.log('ProjectContext: Making API call to check directory');
      const response = await projectApi.check(directoryPath);
      console.log('ProjectContext: API response for check directory:', response);
      return response.data;
    } catch (err) {
      console.error('ProjectContext: Error checking project directory:', err);
      setError('Failed to check directory. Please ensure the path is correct and accessible.');
      return { exists: false, hasVibeArchFolder: false };
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize a new project with .vibearch folder
  const initializeProject = async (directoryPath, projectName) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await projectApi.initialize({
        path: directoryPath,
        name: projectName || directoryPath.split('/').pop()
      });

      // Add to recent projects and set as current
      const newProject = response.data;
      addToRecentProjects(newProject);
      setCurrentProject(newProject);

      return newProject;
    } catch (err) {
      console.error('Error initializing project:', err);
      setError('Failed to initialize project. Please try again.');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Load an existing project
  const loadProject = async (directoryPath) => {
    console.log('ProjectContext: loadProject called with path:', directoryPath);
    setIsLoading(true);
    setError(null);

    // First check if the backend is available
    const backendAvailable = await isBackendAvailable();
    if (!backendAvailable) {
      console.warn('ProjectContext: Backend is not available, using minimal project object');
      setError('Backend server is not available. Using minimal project information.');

      // Create a minimal project object
      const minimalProject = createMinimalProject(directoryPath);

      // Add to recent projects
      addToRecentProjects(minimalProject);

      // Set as current project
      setCurrentProject(minimalProject);
      localStorage.setItem('currentProject', JSON.stringify(minimalProject));

      setIsLoading(false);
      return minimalProject;
    }

    try {
      // First try to use the load endpoint which is more robust
      console.log('ProjectContext: Making API call to load project');
      try {
        const loadResponse = await projectApi.load(directoryPath);
        const project = loadResponse.data;
        console.log('ProjectContext: Project loaded from API (load endpoint):', project);

        // Add to recent projects and set as current
        addToRecentProjects(project);

        // Set current project and ensure it's saved to localStorage
        setCurrentProject(project);
        localStorage.setItem('currentProject', JSON.stringify(project));
        console.log('ProjectContext: Project saved to localStorage');

        return project;
      } catch (loadErr) {
        // Handle the error with our utility function
        handleApiError(loadErr, 'ProjectContext: Error using load endpoint', (msg) => {
          console.warn(msg);
        });

        // Fallback to the get endpoint
        try {
          const response = await projectApi.get(directoryPath);
          const project = response.data;
          console.log('ProjectContext: Project loaded from API (get endpoint):', project);

          // Add to recent projects and set as current
          addToRecentProjects(project);

          // Set current project and ensure it's saved to localStorage
          setCurrentProject(project);
          localStorage.setItem('currentProject', JSON.stringify(project));
          console.log('ProjectContext: Project saved to localStorage');

          return project;
        } catch (getErr) {
          // If both endpoints fail, throw to be caught by the outer catch
          throw getErr;
        }
      }
    } catch (err) {
      // Handle the error with our utility function
      const errorMessage = handleApiError(err, 'ProjectContext: Error loading project', setError);

      // Create a minimal project object as a last resort
      const minimalProject = createMinimalProject(directoryPath);

      console.warn('ProjectContext: Using minimal project object:', minimalProject);

      // Add to recent projects but don't set as current yet
      // The caller will decide whether to use this fallback
      addToRecentProjects(minimalProject);

      return minimalProject;
    } finally {
      setIsLoading(false);
    }
  };

  // Add a project to recent projects list
  const addToRecentProjects = (project) => {
    setRecentProjects(prevProjects => {
      // Remove if already exists
      const filtered = prevProjects.filter(p => p.path !== project.path);
      // Add to beginning of array
      return [project, ...filtered].slice(0, 10); // Keep only 10 most recent
    });
  };

  // Clear current project
  const clearCurrentProject = () => {
    setCurrentProject(null);
    localStorage.removeItem('currentProject');
  };

  return (
    <ProjectContext.Provider
      value={{
        currentProject,
        recentProjects,
        isLoading,
        error,
        checkProjectDirectory,
        initializeProject,
        loadProject,
        clearCurrentProject,
        setCurrentProject
      }}
    >
      {children}
    </ProjectContext.Provider>
  );
};

export default ProjectContext;
