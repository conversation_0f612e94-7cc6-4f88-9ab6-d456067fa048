import { API_URL } from '../services/api';

/**
 * Check if the backend API is available
 * @returns {Promise<boolean>} True if the API is available, false otherwise
 */
export const isBackendAvailable = async () => {
  try {
    // Try to fetch the API health endpoint with a short timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 2000);
    
    const response = await fetch(`${API_URL}/health`, {
      method: 'GET',
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.warn('Backend API is not available:', error);
    return false;
  }
};

/**
 * Create a minimal project object from a path
 * @param {string} path - The project path
 * @param {string} name - Optional project name
 * @returns {Object} A minimal project object
 */
export const createMinimalProject = (path, name = null) => {
  return {
    path,
    name: name || path.split(/[/\\]/).pop(),
    created: new Date().toISOString(),
    isMinimal: true, // Flag to indicate this is a minimal project object
  };
};

/**
 * Handle API errors in a consistent way
 * @param {Error} error - The error object
 * @param {string} context - The context where the error occurred
 * @param {Function} setError - Optional function to set an error message
 * @returns {string} The error message
 */
export const handleApiError = (error, context, setError = null) => {
  let errorMessage = '';
  
  if (error.name === 'AbortError') {
    errorMessage = 'Request timed out. The server might be unavailable.';
  } else if (error.message === 'Network Error' || error.code === 'ERR_NETWORK') {
    errorMessage = 'Network error. Please check your connection and ensure the backend server is running.';
  } else if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    errorMessage = `Server error: ${error.response.status} ${error.response.statusText}`;
    if (error.response.data && error.response.data.message) {
      errorMessage += ` - ${error.response.data.message}`;
    }
  } else if (error.request) {
    // The request was made but no response was received
    errorMessage = 'No response from server. Please check if the backend is running.';
  } else {
    // Something happened in setting up the request that triggered an Error
    errorMessage = error.message || 'Unknown error occurred';
  }
  
  console.error(`${context}: ${errorMessage}`, error);
  
  if (setError) {
    setError(errorMessage);
  }
  
  return errorMessage;
};
