; ========================================================================
; Absolute Minimal Augment Chat Automation Script for AutoHotkey v2
; ========================================================================
; This script uses NO file operations at all to avoid any permission issues
; It performs the absolute minimum steps needed to interact with Augment
;
; Author: Vibe Architect Team
; ========================================================================
; NOTE: This script is designed for AutoHotkey v2
; If you're getting errors, make sure you're using AutoHotkey v2
; The full path to AutoHotkey v2 is typically:
; C:\Program Files\AutoHotkey\v2\AutoHotkey64.exe
; ========================================================================

#Warn All, StdOut
#SingleInstance Force

; STEP 1: Activate VSCode by executable name - try multiple methods
if WinExist("ahk_exe Code.exe") {
    WinActivate
    Sleep(2000)  ; Give it time to fully activate
}
else if WinExist("Visual Studio Code") {
    WinActivate
    Sleep(2000)
}
else if WinExist("- Visual Studio Code") {
    WinActivate
    Sleep(2000)
}
else {
    MsgBox("VSCode window not found. Please make sure VSCode is running.", "Error", "OK")
    ExitApp(1)
}

; STEP 2: Try multiple methods to open Augment chat
; Method 1: Alt+A (custom shortcut that doesn't conflict with Perplexity)
Send("!a")
Sleep(1000)

; Method 2: Ctrl+L (primary Augment shortcut)
Send("^l")
Sleep(2000)

; Method 3: Try clicking where Augment icon might be
; Get window dimensions
WinGetPos(, , &width, &height, "A")
if (width && height) {
    ; Click near the left side of the window where the Augment icon might be
    Click(40, 40)
    Sleep(1000)
}

; STEP 3: Paste the prompt (already in clipboard)
; Click near the bottom of the window where the chat input should be
if (width && height) {
    Click(width / 2, height - 100)
    Sleep(1000)
}

Send("^v")
Sleep(2000)  ; Give it time to paste

; STEP 4: Send the prompt with Enter
Send("{Enter}")
Sleep(1000)

; Show a brief completion message
MsgBox("Prompt sent to Augment", "Success", "T2")

; Exit
ExitApp(0)
