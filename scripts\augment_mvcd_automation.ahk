
; ========================================================================
; Robust Augment Chat Automation Script for AutoHotkey v2
; ========================================================================
; This script uses multiple methods to activate VSCode and Augment
; with fallbacks and detailed logging
;
; The script:
; 1. Assumes the prompt is already in the clipboard
; 2. Uses multiple methods to activate VSCode
; 3. Uses multiple methods to open Augment chat
; 4. Pastes the prompt and sends it
; 5. Provides detailed logging for troubleshooting
;
; Usage:
;   AutoHotkey.exe augment_mvcd_automation.ahk
;
; Requirements:
;   - VSCode with Augment extension installed
;
; Author: Vibe Architect Team
; ========================================================================

; Enable error handling and single instance
#Warn All, StdOut
#SingleInstance Force

; Create debug log
FileDelete("autohotkey_debug.log")
FileAppend("=== Augment Automation Debug Log ===`n`n", "autohotkey_debug.log")
FileAppend("Script started at " . FormatTime(, "yyyy-MM-dd HH:mm:ss") . "`n", "autohotkey_debug.log")

; Debug function to log active window info
LogActiveWindow() {
    activeHwnd := WinGetID("A")
    activeTitle := WinGetTitle("A")
    activeProcess := WinGetProcessName("A")
    activeClass := WinGetClass("A")

    FileAppend("Active Window Info:`n", "autohotkey_debug.log")
    FileAppend("  Title: " . activeTitle . "`n", "autohotkey_debug.log")
    FileAppend("  Process: " . activeProcess . "`n", "autohotkey_debug.log")
    FileAppend("  Class: " . activeClass . "`n", "autohotkey_debug.log")
    FileAppend("  HWND: " . activeHwnd . "`n`n", "autohotkey_debug.log")
}

; Log initial state
FileAppend("Initial window state:`n", "autohotkey_debug.log")
LogActiveWindow()

; ========================================================================
; STEP 1: Activate VSCode using multiple methods
; ========================================================================

FileAppend("Attempting to activate VSCode using multiple methods`n", "autohotkey_debug.log")

; Method 1: Try to activate by window title containing "Visual Studio Code"
FileAppend("Method 1: Activating by window title containing 'Visual Studio Code'`n", "autohotkey_debug.log")
if WinExist("ahk_exe Code.exe") {
    WinActivate
    Sleep(1000)
    FileAppend("VSCode window found by executable name`n", "autohotkey_debug.log")
} else {
    FileAppend("VSCode window NOT found by executable name`n", "autohotkey_debug.log")
}

; Method 2: Try to activate by window title containing "Visual Studio Code"
FileAppend("Method 2: Activating by window title containing 'Visual Studio Code'`n", "autohotkey_debug.log")
if WinExist("Visual Studio Code") {
    WinActivate
    Sleep(1000)
    FileAppend("VSCode window found by title`n", "autohotkey_debug.log")
} else {
    FileAppend("VSCode window NOT found by title`n", "autohotkey_debug.log")
}

; Method 3: Try to activate by window title containing "VSCode"
FileAppend("Method 3: Activating by window title containing 'VSCode'`n", "autohotkey_debug.log")
if WinExist("VSCode") {
    WinActivate
    Sleep(1000)
    FileAppend("VSCode window found by 'VSCode' in title`n", "autohotkey_debug.log")
} else {
    FileAppend("VSCode window NOT found by 'VSCode' in title`n", "autohotkey_debug.log")
}

; Log window state after activation attempts
FileAppend("Window state after VSCode activation attempts:`n", "autohotkey_debug.log")
LogActiveWindow()

; Check if we successfully activated VSCode
activeProcess := WinGetProcessName("A")
if (activeProcess = "Code.exe") {
    FileAppend("SUCCESS: VSCode is now the active window`n", "autohotkey_debug.log")
} else {
    FileAppend("WARNING: Failed to activate VSCode. Current process: " . activeProcess . "`n", "autohotkey_debug.log")
    FileAppend("Continuing anyway...`n", "autohotkey_debug.log")
}

; ========================================================================
; STEP 2: Open Augment Chat using multiple methods
; ========================================================================

FileAppend("Attempting to open Augment Chat using multiple methods`n", "autohotkey_debug.log")

; Method 1: Use Ctrl+L shortcut (primary Augment shortcut)
FileAppend("Method 1: Using Ctrl+L shortcut`n", "autohotkey_debug.log")
Send("^l")
Sleep(2000)

; Log window after first method
FileAppend("Window state after Ctrl+L:`n", "autohotkey_debug.log")
LogActiveWindow()

; Method 2: Use Ctrl+Shift+P to open command palette, then type "Augment"
FileAppend("Method 2: Using Command Palette`n", "autohotkey_debug.log")
Send("^+p")
Sleep(1000)
Send("Augment: Open Chat")
Sleep(500)
Send("{Enter}")
Sleep(2000)

; Log window after second method
FileAppend("Window state after Command Palette:`n", "autohotkey_debug.log")
LogActiveWindow()

; ========================================================================
; STEP 3: Paste the prompt and send it
; ========================================================================

FileAppend("Attempting to paste prompt and send it`n", "autohotkey_debug.log")

; Click in the chat input area (approximate position)
FileAppend("Clicking in chat input area`n", "autohotkey_debug.log")
; Try to click near the bottom of the window
WinGetPos(, , &width, &height, "A")
if (width && height) {
    ; Click near the bottom center of the window
    Click(width / 2, height - 100)
    FileAppend("Clicked at position: " . width / 2 . ", " . (height - 100) . "`n", "autohotkey_debug.log")
} else {
    FileAppend("Could not determine window dimensions`n", "autohotkey_debug.log")
}

Sleep(1000)

; Paste the prompt
FileAppend("Pasting prompt from clipboard`n", "autohotkey_debug.log")
Send("^v")
Sleep(1000)

; Send the prompt
FileAppend("Sending prompt with Enter key`n", "autohotkey_debug.log")
Send("{Enter}")
Sleep(1000)

; ========================================================================
; STEP 4: Completion
; ========================================================================

FileAppend("Automation sequence completed at " . FormatTime(, "yyyy-MM-dd HH:mm:ss") . "`n", "autohotkey_debug.log")
FileAppend("Check VSCode to see if the prompt was successfully sent to Augment.`n", "autohotkey_debug.log")

; Exit the script
ExitApp(0)
