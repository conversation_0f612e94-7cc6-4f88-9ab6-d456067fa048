; ========================================================================
; Cursor Chat Automation Script for AutoHotkey v2
; ========================================================================
; This script automates sending a prompt to the Cursor IDE chat:
; 1. Activates the Cursor IDE window
; 2. Opens a new chat with Ctrl+N
; 3. Pastes the prompt from the clipboard
; 4. Sends the prompt with Enter
; 5. Exits
;
; Author: Vibe Architect Team
; ========================================================================

#Warn All, StdOut
#SingleInstance Force

; STEP 1: Activate Cursor IDE window ONLY
if WinExist("ahk_exe Cursor.exe") {
    WinActivate
    Sleep(1500)
} else if WinExist("Cursor") {
    WinActivate
    Sleep(1500)
} else {
    MsgBox("Cursor IDE window not found. Please make sure Cursor is running.", "Error", "OK")
    ExitApp(1)
}

; STEP 2: Open a new chat (Ctrl+N is the default for Cursor chat)
Send("^n")
Sleep(1000)

; STEP 3: Paste the prompt from clipboard
Send("^v")
Sleep(1000)

; STEP 4: Send the prompt (Enter)
Send("{Enter}")
Sleep(1000)

; STEP 5: Exit
ExitApp(0) 