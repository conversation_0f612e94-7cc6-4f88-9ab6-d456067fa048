; ========================================================================
; Debug Minimal Augment Chat Automation Script for AutoHotkey v2
; ========================================================================
; This script is designed to be as simple as possible while providing
; detailed logging for debugging purposes.
;
; The script:
; 1. Creates a detailed log file
; 2. Activates VSCode
; 3. Opens Augment chat
; 4. Pastes the prompt and sends it
;
; Author: Vibe Architect Team
; ========================================================================

#Warn All, StdOut
#SingleInstance Force

; Create a detailed log file - use user's temp directory to avoid permission issues
logFile := A_Temp . "\debug_minimal.log"

; Try to delete the file if it exists, but don't error if it doesn't
try {
    if FileExist(logFile)
        FileDelete(logFile)
} catch Error as e {
    ; Just continue if we can't delete the file
    MsgBox("Warning: Could not delete log file: " . e.Message, "Warning", "T3")
}

; Try to create the log file
try {
    FileAppend("Script started at " . FormatTime(, "yyyy-MM-dd HH:mm:ss") . "`n", logFile)
    FileAppend("AutoHotkey version: " . A_AhkVersion . "`n", logFile)
    FileAppend("OS: " . A_OSVersion . "`n", logFile)
    FileAppend("Script directory: " . A_ScriptDir . "`n", logFile)
    FileAppend("Working directory: " . A_WorkingDir . "`n", logFile)
    FileAppend("Log file location: " . logFile . "`n", logFile)
} catch Error as e {
    MsgBox("Error creating log file: " . e.Message . "`nContinuing without logging.", "Error", "T5")
    ; Create a dummy function that does nothing for all FileAppend calls
    FileAppend(text, file) {
        return
    }
}

; ========================================================================
; STEP 1: Activate VSCode
; ========================================================================

try FileAppend("`n=== STEP 1: Activate VSCode ===`n", logFile)

; Log all windows first for debugging
try FileAppend("Listing all windows for debugging:`n", logFile)
DetectHiddenWindows(True)
windows := WinGetList()
try FileAppend("Found " . windows.Length . " windows`n", logFile)

for window in windows {
    title := WinGetTitle(window)
    process := WinGetProcessName(window)
    if (title && process) {
        try FileAppend("Window: " . title . " (Process: " . process . ")`n", logFile)
    }
}
DetectHiddenWindows(False)

; Try to activate VSCode by executable name
try FileAppend("Attempting to activate VSCode by executable name`n", logFile)
if WinExist("ahk_exe Code.exe") {
    try FileAppend("VSCode window found, activating...`n", logFile)
    WinActivate
    Sleep(2000)  ; Give it time to fully activate

    activeTitle := WinGetTitle("A")
    activeProcess := WinGetProcessName("A")
    try FileAppend("Active window after activation: " . activeTitle . " (Process: " . activeProcess . ")`n", logFile)
} else {
    try FileAppend("ERROR: VSCode window not found`n", logFile)
    MsgBox("VSCode window not found. Please make sure VSCode is running.", "Error", "OK")
    ExitApp(1)
}

; ========================================================================
; STEP 2: Open Augment Chat
; ========================================================================

try FileAppend("`n=== STEP 2: Open Augment Chat ===`n", logFile)

; Method 1: Use Ctrl+L (primary method)
try FileAppend("Method 1: Using Ctrl+L shortcut`n", logFile)
Send("^l")
Sleep(3000)  ; Give it time to open

; Log the active window after Ctrl+L
activeTitle := WinGetTitle("A")
activeProcess := WinGetProcessName("A")
try FileAppend("Active window after Ctrl+L: " . activeTitle . " (Process: " . activeProcess . ")`n", logFile)

; Method 2: If Ctrl+L doesn't work, try using the Command Palette
try FileAppend("Method 2: Using Command Palette`n", logFile)
Send("^+p")  ; Ctrl+Shift+P for Command Palette
Sleep(1000)
Send("Augment: Open Chat")  ; Type the command
Sleep(1000)
Send("{Enter}")
Sleep(3000)

; Log the active window after Command Palette
activeTitle := WinGetTitle("A")
activeProcess := WinGetProcessName("A")
try FileAppend("Active window after Command Palette: " . activeTitle . " (Process: " . activeProcess . ")`n", logFile)

; ========================================================================
; STEP 3: Paste and Send Prompt
; ========================================================================

try FileAppend("`n=== STEP 3: Paste and Send Prompt ===`n", logFile)

; Get window dimensions
WinGetPos(&x, &y, &width, &height, "A")
try FileAppend("Window position: x=" . x . ", y=" . y . ", width=" . width . ", height=" . height . "`n", logFile)

; Click in the chat input area (bottom of the window)
clickX := width / 2
clickY := height - 50  ; Bottom area of the window
try FileAppend("Clicking at position: x=" . clickX . ", y=" . clickY . "`n", logFile)
Click(clickX, clickY)
Sleep(1000)

; Paste the prompt from clipboard
try FileAppend("Pasting prompt from clipboard`n", logFile)
Send("^v")
Sleep(2000)  ; Give it time to paste

; Send the prompt with Enter
try FileAppend("Sending prompt with Enter`n", logFile)
Send("{Enter}")
Sleep(1000)

; ========================================================================
; STEP 4: Completion
; ========================================================================

try FileAppend("`n=== STEP 4: Completion ===`n", logFile)
try FileAppend("Script completed at " . FormatTime(, "yyyy-MM-dd HH:mm:ss") . "`n", logFile)

; Create a status file to indicate completion
statusFile := A_Temp . "\autohotkey_status.txt"
try {
    if FileExist(statusFile)
        FileDelete(statusFile)
} catch Error as e {
    ; Just continue if we can't delete the file
}

try {
    FileAppend("Completed at " . FormatTime(, "yyyy-MM-dd HH:mm:ss"), statusFile)
    FileAppend("Created status file: " . statusFile . "`n", logFile)
} catch Error as e {
    MsgBox("Error creating status file: " . e.Message, "Warning", "T3")
}

; Exit the script
ExitApp(0)
