; ========================================================================
; Just Open Augment Chat - Minimal Script
; ========================================================================
; This script does ONE thing only: opens a new Augment chat in VSCode
; No window enumeration, no file operations, no clipboard operations
;
; Author: Vibe Architect Team
; ========================================================================

#Warn All, StdOut
#SingleInstance Force

; STEP 1: Activate VSCode (if it's running)
if WinExist("ahk_exe Code.exe") {
    WinActivate
    Sleep(2000)  ; Give it time to fully activate
}

; STEP 2: Open Augment Chat using Ctrl+L ONLY
Send("^l")
Sleep(3000)  ; Give it time to open

; Exit the script
ExitApp(0)
