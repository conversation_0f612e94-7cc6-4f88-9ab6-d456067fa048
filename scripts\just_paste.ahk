; ========================================================================
; Just Open and Paste - No Send
; ========================================================================
; This script opens Augment chat and pastes the prompt, but doesn't send it
; This allows the user to review the prompt before sending
;
; Author: Vibe Architect Team
; ========================================================================

#SingleInstance Force

; Wait a moment before starting to ensure system is ready
Sleep(1000)

; Activate VSCode by executable name
if WinExist("ahk_exe Code.exe") {
    WinActivate
    ; Wait for VSCode to fully activate
    Sleep(3000)
    
    ; Try multiple approaches with pauses between each
    
    ; Approach 1: Alt+A (common shortcut for Augment)
    Send("!a")
    Sleep(4000)
    
    ; Approach 2: Ctrl+L (another common shortcut)
    Send("^l")
    Sleep(4000)
    
    ; Approach 3: Command Palette
    Send("^+p")  ; Ctrl+Shift+P for Command Palette
    Sleep(1000)
    Send("Augment: Open Chat")  ; Type the command
    Sleep(1000)
    Send("{Enter}")
    Sleep(4000)
    
    ; Now paste the prompt but don't send it
    Send("^v")
    Sleep(2000)
}

; Exit the script
ExitApp(0)
