; ========================================================================
; Minimal MVCD Augment Automation Script for AutoHotkey v2
; ========================================================================
; This is the absolute minimal script that tries to activate VSCode,
; open Augment, and paste the prompt with minimal complexity
;
; Author: Vibe Architect Team
; ========================================================================

#Warn All, StdOut
#SingleInstance Force

; Try to create a status file to indicate completion
try {
    statusFile := A_ScriptDir . "\autohotkey_status.txt"
    if FileExist(statusFile)
        FileDelete(statusFile)
} catch Error as e {
    ; If we can't create a status file, just continue
    statusFile := ""
}

; ========================================================================
; STEP 1: Activate VSCode
; ========================================================================

; Try to find VSCode window by executable name
if WinExist("ahk_exe Code.exe") {
    WinActivate
    Sleep(2000)  ; Give it time to fully activate
} else {
    ; Try to find by window title
    if WinExist("Visual Studio Code") {
        WinActivate
        Sleep(2000)
    }
}

; ========================================================================
; STEP 2: Open Augment Chat
; ========================================================================

; Use Ctrl+L shortcut for new Augment chat
Send("^l")
Sleep(5000)  ; Give it plenty of time to open

; Clear any text that might be in the search box
Send("^a")  ; Select all text
Sleep(500)
Send("{Delete}")  ; Delete selected text
Sleep(1000)

; ========================================================================
; STEP 3: Paste and Send Prompt
; ========================================================================

; Get window dimensions
WinGetPos(, , &width, &height, "A")

; Calculate the position of the chat input area - very bottom of the window
chatInputY := height - 20  ; Very bottom of the window

; Click directly in the chat input area
Click(width / 2, chatInputY)
Sleep(2000)

; Make sure any existing text is cleared
Send("^a")  ; Select all
Sleep(500)
Send("{Delete}")  ; Delete selected text
Sleep(1000)

; Now paste the prompt
Send("^v")
Sleep(4000)  ; Give plenty of time for pasting

; Send Enter to submit
Send("{Enter}")
Sleep(3000)

; ========================================================================
; STEP 4: Exit
; ========================================================================

; Create a status file to indicate completion if possible
if (statusFile != "") {
    try {
        FileAppend("Completed", statusFile)
    } catch Error as e {
        ; If we can't write to the status file, just continue
    }
}

; Exit the script
ExitApp(0)
