; ========================================================================
; MVCD Augment Automation Script for AutoHotkey v2
; ========================================================================
; This script activates VSCode, opens Augment, and sends the prompt
; It uses the Alt+A shortcut to avoid conflicts with Perplexity
;
; Author: Vibe Architect Team
; ========================================================================

#Warn All, StdOut
#SingleInstance Force

; Create a log file in the scripts directory
logFile := A_ScriptDir . "\mvcd_augment.log"
FileDelete(logFile)
FileAppend("Script started at " . FormatTime(, "yyyy-MM-dd HH:mm:ss") . "`n", logFile)

; Log system info
FileAppend("AutoHotKey Version: " . A_AhkVersion . "`n", logFile)
FileAppend("OS Version: " . A_OSVersion . "`n", logFile)
FileAppend("Script Directory: " . A_ScriptDir . "`n", logFile)
FileAppend("Working Directory: " . A_WorkingDir . "`n", logFile)

; ========================================================================
; STEP 1: Activate VSCode
; ========================================================================

; Try to find VSCode window by executable name
if WinExist("ahk_exe Code.exe") {
    WinActivate
    Sleep(2000)  ; Give it time to fully activate
} else {
    MsgBox("VSCode window not found. Please make sure VSCode is running.", "Error", "OK")
    ExitApp(1)
}

; Get the active window info
activeTitle := WinGetTitle("A")
activeProcess := WinGetProcessName("A")

; ========================================================================
; STEP 2: Open Augment Chat
; ========================================================================

; Use Alt+A shortcut (this seems to be the most reliable)
Send("!a")
Sleep(3000)  ; Give Augment time to open

; If Alt+A doesn't work, try using the Command Palette
if !InStr(WinGetTitle("A"), "Augment") {
    Send("^+p")  ; Ctrl+Shift+P for Command Palette
    Sleep(1000)
    Send("Augment: Open Chat")  ; Type the command
    Sleep(1000)
    Send("{Enter}")
    Sleep(3000)
}

; Get the active window info again
activeTitle := WinGetTitle("A")
activeProcess := WinGetProcessName("A")

; ========================================================================
; STEP 3: Paste and Send Prompt
; ========================================================================

; Get window dimensions
WinGetPos(, , &width, &height, "A")

; Click in the chat input area (near the bottom of the window)
if (width && height) {
    clickX := width / 2
    clickY := height - 100
    Click(clickX, clickY)
    Sleep(1000)
}

; Paste the prompt from clipboard
Send("^v")
Sleep(2000)  ; Give it time to paste

; Send the prompt with Enter
Send("{Enter}")
Sleep(1000)

; ========================================================================
; STEP 4: Completion
; ========================================================================

; Show a brief completion message
MsgBox("Prompt sent to Augment", "Success", "T2")

; Exit the script
ExitApp(0)
