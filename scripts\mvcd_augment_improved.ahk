; ========================================================================
; MVCD Augment Automation Script for AutoHotkey v2 (IMPROVED VERSION)
; ========================================================================
; This script activates VSCode, opens Augment, and sends the prompt
; with multiple fallback methods and better error handling
;
; Author: Vibe Architect Team
; ========================================================================

#Warn All, StdOut
#SingleInstance Force

; Try to create a simple log file in the scripts directory
try {
    logFile := A_ScriptDir . "\simple_augment.log"
    if FileExist(logFile)
        FileDelete(logFile)
    FileAppend("Script started at " . FormatTime(, "yyyy-MM-dd HH:mm:ss") . "`n", logFile)
} catch Error as e {
    ; If we can't create a log file, just continue without logging
    logFile := ""
}

; Try to create a status file to indicate completion
try {
    statusFile := A_ScriptDir . "\autohotkey_status.txt"
    if FileExist(statusFile)
        FileDelete(statusFile)
} catch Error as e {
    ; If we can't create a status file, just continue without it
    statusFile := ""
}

; ========================================================================
; STEP 1: Activate VSCode using multiple methods
; ========================================================================

; Log if possible
if (logFile != "")
    FileAppend("Attempting to activate VSCode...`n", logFile)

; Method 1: Try to find VSCode window by executable name
if WinExist("ahk_exe Code.exe") {
    WinActivate
    Sleep(2000)  ; Give it time to fully activate
    if (logFile != "")
        FileAppend("VSCode activated by executable name`n", logFile)
} else {
    ; Method 2: Try to find by window title
    if WinExist("Visual Studio Code") {
        WinActivate
        Sleep(2000)
        if (logFile != "")
            FileAppend("VSCode activated by window title`n", logFile)
    } else {
        if (logFile != "")
            FileAppend("VSCode window not found. Attempting to continue anyway...`n", logFile)
    }
}

; ========================================================================
; STEP 2: Open Augment Chat using multiple methods
; ========================================================================

if (logFile != "")
    FileAppend("Attempting to open Augment chat...`n", logFile)

; First try clicking the Augment icon in the sidebar (if visible)
if (logFile != "")
    FileAppend("Trying to click Augment icon in sidebar...`n", logFile)

; Get window dimensions to find the sidebar
WinGetPos(, , &width, &height, "A")
if (width && height) {
    ; Click on the left sidebar where Augment icon likely is
    sidebarX := 30  ; Approximate X position of sidebar icons
    sidebarY := height / 3  ; Try clicking in the upper third of the sidebar

    if (logFile != "")
        FileAppend("Clicking at sidebar position: " . sidebarX . ", " . sidebarY . "`n", logFile)
    Click(sidebarX, sidebarY)
    Sleep(2000)
}

; Now use Ctrl+L (primary method)
if (logFile != "")
    FileAppend("Trying Ctrl+L shortcut...`n", logFile)
Send("^l")  ; Ctrl+L shortcut for new Augment chat
Sleep(5000)  ; Give it more time to open

; Clear any text that might be in the search box
if (logFile != "")
    FileAppend("Clearing any text in search box...`n", logFile)
Send("^a")  ; Select all text
Sleep(500)
Send("{Delete}")  ; Delete selected text
Sleep(1000)

; Fallback: Try using Command Palette to open Augment
if (logFile != "")
    FileAppend("Trying Command Palette fallback...`n", logFile)
Send("^+p")  ; Open command palette
Sleep(1000)
Send("Augment: New Chat")  ; Type the command
Sleep(1000)
Send("{Enter}")  ; Execute the command
Sleep(3000)

; ========================================================================
; STEP 3: Paste and Send Prompt
; ========================================================================

if (logFile != "")
    FileAppend("Attempting to paste and send prompt...`n", logFile)

; Wait to make sure the chat is fully loaded
Sleep(3000)

; Make sure any existing text is cleared
if (logFile != "")
    FileAppend("Clearing any existing text...`n", logFile)
Send("^a")  ; Select all
Sleep(500)
Send("{Delete}")  ; Delete selected text
Sleep(1000)

; Paste the prompt
if (logFile != "")
    FileAppend("Pasting prompt...`n", logFile)
Send("^v")
Sleep(2000)  ; Give time for pasting

; Send Enter to submit
if (logFile != "")
    FileAppend("Sending Enter to submit...`n", logFile)
Send("{Enter}")
Sleep(2000)

; ========================================================================
; STEP 4: Completion
; ========================================================================

; Create a status file to indicate completion if possible
if (statusFile != "") {
    try {
        FileAppend("Automation completed at " . FormatTime(, "yyyy-MM-dd HH:mm:ss"), statusFile)
    } catch Error as e {
        ; If we can't write to the status file, just continue
    }
}

; Log completion if possible
if (logFile != "") {
    try {
        FileAppend("Script completed at " . FormatTime(, "yyyy-MM-dd HH:mm:ss") . "`n", logFile)
    } catch Error as e {
        ; If we can't write to the log file, just continue
    }
}

; Log completion without trying to reactivate windows
; This avoids accidentally selecting desktop files
if (logFile != "")
    FileAppend("Script completed successfully. No window reactivation to avoid desktop selection.`n", logFile)

; Don't use ExitApp(0) as it might be collapsing VSCode
; Just let the script end naturally
if (logFile != "")
    FileAppend("Script ending naturally without ExitApp command...`n", logFile)
