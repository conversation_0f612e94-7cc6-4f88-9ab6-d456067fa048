; ========================================================================
; MVCD Augment Automation Script for AutoHotkey v2
; ========================================================================
; This script activates VSCode, opens Augment, and sends the prompt
; It uses the Alt+A shortcut to avoid conflicts with Perplexity
;
; Author: Vibe Architect Team
; ========================================================================

#Warn All, StdOut
#SingleInstance Force

; No logging to avoid permission issues

; ========================================================================
; STEP 1: Activate VSCode
; ========================================================================

; Try to find VSCode window by executable name
if WinExist("ahk_exe Code.exe") {
    WinActivate
    Sleep(2000)  ; Give it time to fully activate
} else {
    MsgBox("VSCode window not found. Please make sure VSCode is running.", "Error", "OK")
    ExitApp(1)
}

; ========================================================================
; STEP 2: Open Augment Chat
; ========================================================================

; First, make sure VSCode is active
WinGetPos(, , &width, &height, "A")
if (width && height) {
    ; Use Ctrl+L to open a new Augment chat
    Send("^l")  ; Ctrl+L shortcut for new Augment chat
    Sleep(5000)  ; Give it more time to fully open

    ; If Ctrl+L doesn't work, try using the Command Palette
    Send("^+p")  ; Ctrl+Shift+P for Command Palette
    Sleep(1000)
    Send("Augment: Open Chat")  ; Type the command
    Sleep(1000)
    Send("{Enter}")
    Sleep(3000)
}

; ========================================================================
; STEP 3: Paste and Send Prompt
; ========================================================================

; The Augment chat input should be focused automatically after Ctrl+L
; But let's make sure by clicking in the input area

; Get updated window dimensions
WinGetPos(, , &width, &height, "A")

; Click in the chat input area (blue field in the screenshot)
clickX := width / 2
clickY := height - 50  ; Bottom area of the window
Click(clickX, clickY)
Sleep(2000)

; Paste the prompt from clipboard
Send("^v")
Sleep(3000)  ; Give it more time to paste

; Send the prompt with Enter
Send("{Enter}")
Sleep(2000)

; ========================================================================
; STEP 4: Completion
; ========================================================================

; Show a brief completion message
MsgBox("Prompt sent to Augment", "Success", "T2")

; Exit the script
ExitApp(0)
