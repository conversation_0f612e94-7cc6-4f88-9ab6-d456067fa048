; ========================================================================
; No Window Enumeration Augment Chat Automation Script for AutoHotkey v2
; ========================================================================
; This script avoids enumerating windows to prevent access denied errors.
; It simply:
; 1. Activates VSCode directly
; 2. Opens Augment chat
; 3. Pastes the prompt and sends it
;
; Author: Vibe Architect Team
; ========================================================================

#Warn All, StdOut
#SingleInstance Force

; Create a simple status file to indicate the script is running
statusFile := A_Temp . "\autohotkey_status.txt"
try {
    FileDelete(statusFile)
    FileAppend("Started at " . FormatTime(, "yyyy-MM-dd HH:mm:ss"), statusFile)
} catch Error as e {
    ; Ignore errors with the status file
}

; ========================================================================
; STEP 1: Activate VSCode
; ========================================================================

; Try to activate VSCode by executable name
if WinExist("ahk_exe Code.exe") {
    WinActivate
    Sleep(2000)  ; Give it time to fully activate
} else {
    MsgBox("VSCode window not found. Please make sure VSCode is running.", "Error", "OK")
    ExitApp(1)
}

; ========================================================================
; STEP 2: Open Augment Chat
; ========================================================================

; Method 1: Use Ctrl+L (primary method)
Send("^l")
Sleep(3000)  ; Give it time to open

; Method 2: If Ctrl+L doesn't work, try using the Command Palette
Send("^+p")  ; Ctrl+Shift+P for Command Palette
Sleep(1000)
Send("Augment: Open Chat")  ; Type the command
Sleep(1000)
Send("{Enter}")
Sleep(3000)

; ========================================================================
; STEP 3: Paste and Send Prompt
; ========================================================================

; Get window dimensions
WinGetPos(&x, &y, &width, &height, "A")

; Click in the chat input area (bottom of the window)
clickX := width / 2
clickY := height - 50  ; Bottom area of the window
Click(clickX, clickY)
Sleep(1000)

; Paste the prompt from clipboard
Send("^v")
Sleep(2000)  ; Give it time to paste

; Send the prompt with Enter
Send("{Enter}")
Sleep(1000)

; Update the status file to indicate completion
try {
    FileDelete(statusFile)
    FileAppend("Completed at " . FormatTime(, "yyyy-MM-dd HH:mm:ss"), statusFile)
} catch Error as e {
    ; Ignore errors with the status file
}

; Exit the script
ExitApp(0)
