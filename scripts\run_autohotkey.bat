@echo off
echo ========================================
echo MVCD Enrichment Automation
echo ========================================
echo Running PowerShell script...

REM Run the PowerShell script directly - no fallbacks
powershell -ExecutionPolicy Bypass -File "%~dp0run_mvcd_augment.ps1"

if %ERRORLEVEL% EQU 0 (
    echo PowerShell script executed successfully.
    echo Success > "%~dp0autohotkey_status.txt"
) else (
    echo ERROR: PowerShell script failed with error code %ERRORLEVEL%
    echo Failed > "%~dp0autohotkey_status.txt"
    pause
    exit /b 1
)

echo ========================================
echo Press any key to continue...
pause
