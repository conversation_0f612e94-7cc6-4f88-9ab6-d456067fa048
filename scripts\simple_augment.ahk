; ========================================================================
; Ultra-Simple Augment Chat Automation Script for AutoHotkey v2
; ========================================================================
; This script uses the absolute minimum steps to activate Augment and paste a prompt
; with no extra commands that might trigger other applications
;
; Author: Vibe Architect Team
; ========================================================================

#Warn All, StdOut
#SingleInstance Force

; Create a simple log file
logFile := A_ScriptDir . "\simple_augment.log"
FileDelete(logFile)
FileAppend("Script started at " . FormatTime(, "yyyy-MM-dd HH:mm:ss") . "`n", logFile)

; STEP 1: Activate VSCode by executable name
FileAppend("Activating VSCode by executable name`n", logFile)
if WinExist("ahk_exe Code.exe") {
    WinActivate
    Sleep(2000)  ; Give it time to fully activate
    FileAppend("VSCode window activated`n", logFile)
} else {
    FileAppend("ERROR: VSCode window not found`n", logFile)
    MsgBox("VSCode window not found. Please make sure VSCode is running.", "Error", "OK")
    ExitApp(1)
}

; STEP 2: Open Augment with Alt+A only
FileAppend("Opening Augment with Alt+A`n", logFile)
Send("!a")
Sleep(3000)  ; Give Augment time to open

; STEP 3: Paste the prompt (already in clipboard)
FileAppend("Pasting prompt from clipboard`n", logFile)
Send("^v")
Sleep(2000)  ; Give it time to paste

; STEP 4: Send the prompt with Enter
FileAppend("Sending prompt with Enter`n", logFile)
Send("{Enter}")
Sleep(1000)

; Log completion
FileAppend("Script completed at " . FormatTime(, "yyyy-MM-dd HH:mm:ss") . "`n", logFile)

; Show a brief completion message
MsgBox("Prompt sent to Augment", "Success", "T2")

; Exit
ExitApp(0)
