"""
Simple test script to verify that Python can create log files.
This is used by the MVCD enrichment test script to verify basic functionality.
"""

import os
import sys
import datetime

def main():
    """Create test log files and print a success message."""
    # Create a log file in the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    log_path = os.path.join(script_dir, "test_log.log")
    
    with open(log_path, "w") as f:
        f.write(f"Test script executed successfully at {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    print(f"Test script executed successfully at {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Log file created at: {log_path}")
    
    # Create a log file in the root directory
    root_dir = os.path.dirname(script_dir)
    root_log_path = os.path.join(root_dir, "test_root.log")
    
    with open(root_log_path, "w") as f:
        f.write(f"Test script executed successfully at {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    print(f"Root log file created at: {root_log_path}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
