; ========================================================================
; Ultra Minimal Augment Chat Opener
; ========================================================================
; This script does the absolute minimum to open an Augment chat
; with careful timing and no extra operations
;
; Author: Vibe Architect Team
; ========================================================================

#SingleInstance Force

; Wait a moment before starting to ensure system is ready
Sleep(1000)

; Activate VSCode by executable name
if WinExist("ahk_exe Code.exe") {
    WinActivate
    ; Wait for VSCode to fully activate
    Sleep(3000)
    
    ; Use Alt+A shortcut which is often used for Augment
    Send("!a")
    Sleep(4000)
}

; Exit the script
ExitApp(0)
