<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #f5f5f5;
            white-space: pre-wrap;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        input {
            padding: 8px;
            width: 400px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>API Test</h1>
    
    <div>
        <label for="apiUrl">API URL:</label>
        <input type="text" id="apiUrl" value="http://localhost:5002/api" />
    </div>
    
    <div>
        <label for="projectPath">Project Path:</label>
        <input type="text" id="projectPath" value="C:/Users/<USER>/Documents/VSCode/Vibearch" />
    </div>
    
    <div style="margin-top: 20px;">
        <button onclick="testHealth()">Test Health</button>
        <button onclick="testMvcdStatus()">Test MVCD Status</button>
        <button onclick="testProjects()">Test Projects</button>
    </div>
    
    <div id="result" class="result">Results will appear here...</div>
    
    <script>
        function displayResult(title, data, error = null) {
            const resultDiv = document.getElementById('result');
            
            if (error) {
                resultDiv.innerHTML = `<h3>${title}</h3><p style="color: red;">Error: ${error.message || error}</p>`;
                if (error.response) {
                    resultDiv.innerHTML += `<p>Status: ${error.response.status}</p>`;
                    resultDiv.innerHTML += `<p>Data: ${JSON.stringify(error.response.data, null, 2)}</p>`;
                }
                console.error('API Error:', error);
            } else {
                resultDiv.innerHTML = `<h3>${title}</h3><p>${JSON.stringify(data, null, 2)}</p>`;
                console.log('API Response:', data);
            }
        }
        
        async function testHealth() {
            try {
                const apiUrl = document.getElementById('apiUrl').value;
                const baseUrl = apiUrl.replace('/api', '');
                
                console.log('Testing health endpoint:', `${baseUrl}/health`);
                const response = await axios.get(`${baseUrl}/health`);
                displayResult('Health Check', response.data);
            } catch (error) {
                displayResult('Health Check', null, error);
            }
        }
        
        async function testMvcdStatus() {
            try {
                const apiUrl = document.getElementById('apiUrl').value;
                const projectPath = document.getElementById('projectPath').value;
                
                console.log('Testing MVCD status endpoint:', `${apiUrl}/mvcd/status?project_path=${encodeURIComponent(projectPath)}`);
                const response = await axios.get(`${apiUrl}/mvcd/status?project_path=${encodeURIComponent(projectPath)}`);
                displayResult('MVCD Status', response.data);
            } catch (error) {
                displayResult('MVCD Status', null, error);
            }
        }
        
        async function testProjects() {
            try {
                const apiUrl = document.getElementById('apiUrl').value;
                const projectPath = document.getElementById('projectPath').value;
                
                console.log('Testing projects endpoint:', `${apiUrl}/projects?base_directory=${encodeURIComponent(projectPath)}`);
                const response = await axios.get(`${apiUrl}/projects?base_directory=${encodeURIComponent(projectPath)}`);
                displayResult('Projects', response.data);
            } catch (error) {
                displayResult('Projects', null, error);
            }
        }
    </script>
</body>
</html>
