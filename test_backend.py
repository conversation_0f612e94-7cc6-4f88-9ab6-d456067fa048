import sys
import os
from pathlib import Path

# Add the current directory to the Python path
current_dir = Path.cwd()
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

try:
    print("Trying to import backend modules...")
    from backend.app.main import app
    print("Successfully imported app from backend.app.main")
    
    print("Checking API router...")
    from backend.app.api.router import api_router
    print("Successfully imported api_router")
    
    print("Checking MVCD endpoints...")
    from backend.app.api.endpoints.mvcd import router as mvcd_router
    print("Successfully imported mvcd_router")
    
    print("All imports successful!")
except Exception as e:
    print(f"Error importing backend modules: {e}")
    import traceback
    traceback.print_exc()
