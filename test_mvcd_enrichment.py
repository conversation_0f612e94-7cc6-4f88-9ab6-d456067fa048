"""
Test script to verify the MVCD enrichment process.
This script can be run directly to test the MVCD enrichment process without going through the UI.
"""

import os
import sys
import time
import datetime
import subprocess
import threading

def create_log_file(log_path):
    """Create a log file with basic information."""
    with open(log_path, "w") as f:
        f.write(f"MVCD Enrichment Test - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Python executable: {sys.executable}\n")
        f.write(f"Python version: {sys.version}\n")
        f.write(f"Current working directory: {os.getcwd()}\n")
        f.write(f"Script path: {os.path.abspath(__file__)}\n")
        f.write("\n")

    print(f"Log file created at: {log_path}")

def log_message(log_path, message, level="INFO"):
    """Append a message to the log file."""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(log_path, "a") as f:
        f.write(f"{timestamp} [{level}] {message}\n")

    if level == "ERROR":
        print(f"{timestamp} [{level}] {message}", file=sys.stderr)
    else:
        print(f"{timestamp} [{level}] {message}")

def run_test_script(project_path, log_path):
    """Run the test_log.py script to verify basic functionality."""
    test_script_path = os.path.join(project_path, "scripts", "test_log.py")

    if not os.path.exists(test_script_path):
        log_message(log_path, f"Test script not found: {test_script_path}", "ERROR")
        return False

    log_message(log_path, f"Running test script: {test_script_path}")

    try:
        result = subprocess.run(
            [sys.executable, test_script_path],
            capture_output=True,
            text=True,
            check=True
        )

        log_message(log_path, "Test script output:")
        log_message(log_path, result.stdout)
        if result.stderr:
            log_message(log_path, f"Errors: {result.stderr}", "ERROR")

        log_message(log_path, "Test script completed successfully", "SUCCESS")
        return True
    except Exception as e:
        log_message(log_path, f"Error running test script: {str(e)}", "ERROR")
        return False

def check_autohotkey_installed():
    """Check if AutoHotKey is installed and in the PATH."""
    try:
        # Try to run AutoHotKey with the version flag
        result = subprocess.run(
            ["AutoHotkey.exe", "/?"],
            capture_output=True,
            text=True,
            timeout=5  # 5 second timeout
        )
        return True
    except (subprocess.SubprocessError, FileNotFoundError):
        return False

def find_autohotkey_path():
    """Try to find the AutoHotKey executable."""
    # Common installation paths
    paths = [
        r"C:\Program Files\AutoHotkey\AutoHotkey.exe",
        r"C:\Program Files\AutoHotkey\v2\AutoHotkey64.exe",
        r"C:\Program Files\AutoHotkey\v2\AutoHotkey32.exe",
        r"C:\Program Files (x86)\AutoHotkey\AutoHotkey.exe",
        r"C:\Program Files (x86)\AutoHotkey\v2\AutoHotkey.exe"
    ]

    for path in paths:
        if os.path.exists(path):
            return path

    return None

def run_autohotkey_script(project_path, log_path):
    """Run the AutoHotKey script to automate Augment interaction."""
    # Check if AutoHotKey is installed
    log_message(log_path, "Checking if AutoHotKey is installed")

    if not check_autohotkey_installed():
        ahk_path = find_autohotkey_path()
        if ahk_path:
            log_message(log_path, f"AutoHotKey found at {ahk_path} but not in PATH", "WARNING")
        else:
            log_message(log_path, "AutoHotKey is not installed or not in PATH", "ERROR")
            log_message(log_path, "Please install AutoHotKey from https://www.autohotkey.com/", "ERROR")
            log_message(log_path, "After installation, add it to your PATH or restart your computer", "ERROR")
            return False

    # Try multiple scripts in order of preference
    script_paths = [
        os.path.join(project_path, "scripts", "just_paste.ahk"),  # Our new just paste script
        os.path.join(project_path, "scripts", "multi_approach.ahk"),
        os.path.join(project_path, "scripts", "ultra_minimal.ahk"),
        os.path.join(project_path, "scripts", "no_window_enum.ahk"),
        os.path.join(project_path, "scripts", "absolute_minimal.ahk")
    ]

    # Find the first script that exists
    script_to_use = None
    for script_path in script_paths:
        if os.path.exists(script_path):
            script_to_use = script_path
            log_message(log_path, f"Found script: {script_path}")
            break

    if not script_to_use:
        log_message(log_path, "No AutoHotKey scripts found", "ERROR")
        return False

    log_message(log_path, f"Using script: {script_to_use}")

    # Try to run the script
    try:
        log_message(log_path, "Running AutoHotKey script")

        # Try with explicit path if found
        ahk_path = find_autohotkey_path()
        if ahk_path:
            log_message(log_path, f"Using AutoHotKey at: {ahk_path}")
            result = subprocess.run(
                [ahk_path, script_to_use],
                capture_output=True,
                text=True,
                check=True
            )
        else:
            # Try with just the command (relies on PATH)
            log_message(log_path, "Using AutoHotKey from PATH")
            result = subprocess.run(
                ["AutoHotkey.exe", script_to_use],
                capture_output=True,
                text=True,
                check=True
            )

        log_message(log_path, "AutoHotKey script output:")
        if result.stdout:
            log_message(log_path, result.stdout)
        if result.stderr:
            log_message(log_path, f"Errors: {result.stderr}", "ERROR")

        log_message(log_path, "AutoHotKey script completed successfully", "SUCCESS")
        return True
    except Exception as e:
        log_message(log_path, f"Error running AutoHotKey script: {str(e)}", "ERROR")
        return False

def main():
    # Get the project path (current directory)
    project_path = os.getcwd()

    # Create a log file
    log_path = os.path.join(project_path, "mvcd_test.log")
    create_log_file(log_path)

    log_message(log_path, f"Project path: {project_path}")

    # Run the test script
    if not run_test_script(project_path, log_path):
        log_message(log_path, "Test script failed, aborting", "ERROR")
        return 1

    # Run the AutoHotKey script
    if not run_autohotkey_script(project_path, log_path):
        log_message(log_path, "AutoHotKey script failed", "ERROR")
        return 1

    log_message(log_path, "Test completed successfully", "SUCCESS")
    return 0

if __name__ == "__main__":
    sys.exit(main())
