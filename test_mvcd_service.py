import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.abspath("backend"))

# Import the MVCDService
from app.services.mvcd_service import MVCDService

print("=" * 80)
print("MVCD Service - Minimum Viable Code Description Generator")
print("=" * 80)
print("This tool scans the codebase and generates a structured YAML file")
print("describing all code elements, their types, dependencies, and lines of code.")
print()
print("The generated file will be saved to: .VibeArch/Directory/mvcd.yaml")
print()
print("Next steps after generation:")
print("1. Use a coding agent (LLM) with the enrichment prompt to add descriptions")
print("2. The agent will analyze code files and add meaningful descriptions")
print("3. The agent will also assign confidence scores to each description")
print("4. Commit the updated mvcd.yaml file to Git to track documentation evolution")
print()
print("Starting codebase scan...")

# Create an instance of the service
mvcd_service = MVCDService()

# Generate the MVCD file
mvcd_service.generate()

print()
print("MVCD file generated successfully!")
print("=" * 80)
print("To enrich the descriptions, use the prompt at:")
print(".VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.md")
print("=" * 80)
