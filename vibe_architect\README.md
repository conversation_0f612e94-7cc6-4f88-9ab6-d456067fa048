# Vibe Architect

An orchestrator tool that manages and coordinates AI-based coding agents while maintaining structural clarity and code quality in a single repository.

## Features

- Select and supervise a project directory
- Interface with LLMs and coding tools
- Track project evolution through a maintained `.md` spec
- Maintain a development task list ([todo], [done])
- Offer a visual representation of workflow structure (graph-based UI)

## Installation

```bash
pip install vibe-architect
```

## Quick Start

```bash
# Initialize a new project
vibearch init /path/to/project

# Add a new task
vibearch add-task "Implement user login"

# Mark a task as done
vibearch mark-done "Invoice CRUD"

# Generate a visual graph of the project
vibearch graph
```

## Documentation

For more detailed information, see the [documentation](docs/usage.md).

## License

MIT