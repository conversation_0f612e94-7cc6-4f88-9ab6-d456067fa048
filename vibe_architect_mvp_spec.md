# Vibe Architect MVP Specification (v0.1)

## 🧭 Purpose
This is the **minimal viable spec** for the first version of Vibe Architect — an orchestrator tool that manages and coordinates AI-based coding agents while maintaining structural clarity and code quality in a single repository. It is designed to:

- Select and supervise a project directory
- Interface with LLMs and coding tools
- Track project evolution through a maintained `.md` spec
- Maintain a development task list ([todo], [done])
- Offer a visual representation of workflow structure (graph-based UI)

---

## 🗂️ Directory Setup
- User selects a **target project directory**
- Initializes or updates a `.vibearchitect/` folder in that directory
- Creates or syncs with:
  - `spec.md` → Human-readable architecture + decision log
  - `tasks.md` → Development task tracker ([todo] / [done])
  - `settings.yaml` → Tool config (LLM, API keys, structure)

---

## ⚙️ Configurable Settings (`settings.yaml`)
```yaml
llm_provider: openai
llm_model: gpt-4o
api_keys:
  openai: sk-xxxxx
  other_tools: {}
project_structure:
  frontend: src/frontend/
  backend: src/backend/
  api: src/api/
```

---

## 📄 `spec.md` File
Tracks the evolving understanding of the project, including:
- Feature definitions
- Structural guidelines
- Reasoning behind architectural decisions
- LLM prompt templates or instructions

Example:
```md
## Feature: Invoice CRUD
- Implemented with async SQLAlchemy
- Routes → FastAPI
- Service → `src/backend/invoice_service.py`
- [done] Validated by architect on 2025-05-08
```

---

## ✅ Task Management (`tasks.md`)
Tracks project progress using markdown checklists:
```md
## [todo]
- [ ] Add user auth routes
- [ ] Refactor invoice model
- [ ] Create test coverage script

## [done]
- [x] Invoice CRUD
- [x] Folder structure validated
```

---

## 🧠 LLM Integration
- Supports OpenAI-compatible API (default: `gpt-4o`)
- Allows sending:
  - Prompts for task planning
  - Prompts for code generation (via external call)
- Future: support Claude, local models via adapters

---

## 🧱 Tech Stack (MVP)
- Python 3.11+
- Typer or Click (CLI tool)
- PyYAML (for config)
- Rich (console UI)
- networkx or Graphviz (optional: graphical task/workflow view)
- Optional: FastAPI (for future web UI)

---

## 🖼️ Graphical Workflow (Preview-Only in v0.1)
- Auto-generates a basic task/feature graph using `networkx` + `matplotlib` or Graphviz
- Nodes = tasks or components
- Edges = dependencies or ownership links

---

## 🔄 Basic CLI Commands
```bash
vibearch init <path>
  → Initialize directory and config files

vibearch set-model gpt-4-turbo
vibearch add-task "Implement user login"
vibearch mark-done "Invoice CRUD"
vibearch graph
  → Generate PNG/SVG of task structure
```

---

## 🛠️ Future Extensions (Beyond MVP)
- Agent orchestration (Cursor, Augment)
- MCP/A2A integration
- Policy engine
- Test/lint validation hooks
- Web UI

---

_Last updated: 2025-05-08_
